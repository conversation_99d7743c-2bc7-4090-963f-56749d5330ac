# 🌌 Eclipse Softworks - Luna/Space Theme Website

A stunning space-themed website for Eclipse Softworks (Pty) Ltd, featuring cosmic animations, stellar design, and cutting-edge technology.

![Eclipse Softworks](public/eclipse-logo.svg)

## ✨ Features

### 🎨 Design & Theme
- **Luna/Space Theme**: Cosmic color palette with deep space backgrounds
- **Animated Starfield**: Twinkling stars and floating cosmic elements
- **Eclipse Logo**: Custom animated SVG logo with orbital elements
- **Gradient Typography**: Orbitron font with cosmic gradient effects
- **Responsive Design**: Mobile-first approach with stellar UX

### 🚀 Technology Stack
- **React 18**: Modern React with hooks and functional components
- **Vite**: Lightning-fast build tool and development server
- **Framer Motion**: Smooth animations and micro-interactions
- **Tailwind CSS**: Utility-first CSS with custom space theme
- **Custom Fonts**: Orbitron (headings) + Inter (body text)

### 🌟 Components
- **Hero Section**: Animated landing with cosmic call-to-action
- **Services**: 6 space-themed service cards with hover effects
- **About**: Company story with orbital visualization
- **Contact**: Cosmic contact form with transmission theme
- **Footer**: Comprehensive footer with social links and branding

### 🎭 Animations
- **Starfield Background**: Animated twinkling stars
- **Floating Elements**: Cosmic particles and nebula effects
- **Hover Interactions**: Scale, glow, and rotation effects
- **Scroll Animations**: Reveal animations on scroll
- **Loading States**: Smooth transitions and micro-interactions

## 🛠️ Development

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd Eclipse_Softworks

# Install dependencies
npm install

# Start development server
npm run dev
```

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

### Project Structure
```
src/
├── components/
│   ├── EclipseLogo.jsx    # Animated logo component
│   ├── Navigation.jsx     # Header navigation
│   ├── Hero.jsx          # Landing section
│   ├── Services.jsx      # Services showcase
│   ├── About.jsx         # Company information
│   ├── Contact.jsx       # Contact form
│   └── Footer.jsx        # Site footer
├── App.jsx               # Main application
├── main.jsx             # Entry point
└── index.css            # Global styles
```

## 🎨 Design System

### Color Palette
```css
/* Primary Colors */
--space-dark: #0A0F2C      /* Deep space background */
--space-blue: #38BDF8      /* Primary accent */
--space-purple: #8B5CF6    /* Secondary accent */
--space-cyan: #06B6D4      /* Tertiary accent */

/* Text Colors */
--star-white: #FFFFFF      /* Primary text */
--star-silver: #CBD5E1     /* Secondary text */

/* Gradients */
--cosmic-gradient: linear-gradient(135deg, #38BDF8, #8B5CF6, #06B6D4)
```

### Typography
- **Headings**: Orbitron (futuristic, space-themed)
- **Body**: Inter (clean, readable)
- **Sizes**: Responsive scale from mobile to desktop

### Animations
- **Float**: Gentle up/down movement
- **Pulse Glow**: Pulsing glow effect
- **Twinkle**: Star twinkling animation
- **Drift**: Slow horizontal movement

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your Git repository to Vercel
2. Configure build settings:
   - Framework: Vite
   - Build Command: `npm run build`
   - Output Directory: `dist`
3. Deploy automatically on push

See [DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md) for detailed instructions.

### Other Platforms
- **Netlify**: Drag & drop `dist` folder
- **GitHub Pages**: Use `gh-pages` branch
- **AWS S3**: Upload `dist` contents to bucket

## 📱 PWA Support

The site includes Progressive Web App features:
- **Manifest**: `public/site.webmanifest`
- **Icons**: Multiple sizes for all devices
- **Offline Ready**: Service worker for caching
- **Add to Home Screen**: Native app-like experience

## 🔧 Customization

### Colors
Edit `tailwind.config.js` to modify the space color palette:
```javascript
colors: {
  'space-dark': '#0A0F2C',
  'space-blue': '#38BDF8',
  // Add your custom colors
}
```

### Animations
Modify animations in `tailwind.config.js`:
```javascript
animation: {
  'float': 'float 6s ease-in-out infinite',
  'pulse-glow': 'pulse-glow 2s ease-in-out infinite',
  // Add custom animations
}
```

### Content
Update component content in respective files:
- Hero text: `src/components/Hero.jsx`
- Services: `src/components/Services.jsx`
- About info: `src/components/About.jsx`

## 📊 Performance

### Optimizations
- ✅ Vite build optimization
- ✅ Tree shaking and code splitting
- ✅ Image optimization
- ✅ CSS purging
- ✅ Gzip compression
- ✅ Lazy loading

### Metrics
- **Lighthouse Score**: 95+ (Performance, Accessibility, SEO)
- **Bundle Size**: ~277KB (gzipped: ~87KB)
- **First Paint**: <1s
- **Interactive**: <2s

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

© 2024 Eclipse Softworks (Pty) Ltd. All rights reserved.

## 📞 Contact

- **Website**: [eclipse-softworks.com](https://eclipse-softworks.com)
- **Email**: <EMAIL>
- **Location**: South Africa & Beyond

---

*Made with 🚀 and cosmic inspiration*
