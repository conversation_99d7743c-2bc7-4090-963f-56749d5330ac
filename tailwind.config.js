/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'space-dark': '#0A0F2C',
        'space-blue': '#38BDF8',
        'space-purple': '#8B5CF6',
        'space-indigo': '#6366F1',
        'space-cyan': '#06B6D4',
        'nebula-pink': '#EC4899',
        'nebula-purple': '#A855F7',
        'star-white': '#F8FAFC',
        'star-silver': '#E2E8F0',
      },
      fontFamily: {
        'orbitron': ['Orbitron', 'monospace'],
        'inter': ['Inter', 'sans-serif'],
        'sans': ['Inter', 'system-ui', 'sans-serif'],
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'pulse-glow': 'pulse-glow 2s ease-in-out infinite alternate',
        'twinkle': 'twinkle 3s ease-in-out infinite',
        'drift': 'drift 20s linear infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        'pulse-glow': {
          '0%': { boxShadow: '0 0 5px #38BDF8, 0 0 10px #38BDF8, 0 0 15px #38BDF8' },
          '100%': { boxShadow: '0 0 10px #38BDF8, 0 0 20px #38BDF8, 0 0 30px #38BDF8' },
        },
        twinkle: {
          '0%, 100%': { opacity: '0.3' },
          '50%': { opacity: '1' },
        },
        drift: {
          '0%': { transform: 'translateX(-100px)' },
          '100%': { transform: 'translateX(calc(100vw + 100px))' },
        },
      },
      backgroundImage: {
        'space-gradient': 'linear-gradient(135deg, #0A0F2C 0%, #1E1B4B 50%, #312E81 100%)',
        'nebula-gradient': 'radial-gradient(ellipse at center, rgba(139, 92, 246, 0.3) 0%, rgba(59, 130, 246, 0.2) 50%, transparent 70%)',
      },
    },
  },
  plugins: [],
}
