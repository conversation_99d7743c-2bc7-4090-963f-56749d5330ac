<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients -->
    <radialGradient id="eclipseGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#38BDF8;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8B5CF6;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#06B6D4;stop-opacity:0.6" />
    </radialGradient>
    
    <radialGradient id="shadowGradient" cx="30%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#0A0F2C;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#0A0F2C;stop-opacity:0.3" />
    </radialGradient>
    
    <linearGradient id="starGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#38BDF8;stop-opacity:0.8" />
    </linearGradient>
    
    <!-- Glow filter -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- Animation -->
    <animateTransform id="rotate" attributeName="transform" type="rotate" 
                      values="0 100 100;360 100 100" dur="20s" repeatCount="indefinite"/>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="95" fill="url(#shadowGradient)" opacity="0.3"/>
  
  <!-- Main eclipse body -->
  <circle cx="100" cy="100" r="80" fill="url(#eclipseGradient)" filter="url(#glow)"/>
  
  <!-- Eclipse shadow -->
  <ellipse cx="120" cy="100" rx="60" ry="80" fill="url(#shadowGradient)" opacity="0.7"/>
  
  <!-- Orbital rings -->
  <g>
    <animateTransform attributeName="transform" type="rotate" 
                      values="0 100 100;360 100 100" dur="15s" repeatCount="indefinite"/>
    <circle cx="100" cy="100" r="90" fill="none" stroke="#38BDF8" stroke-width="1" opacity="0.4"/>
    <circle cx="100" cy="100" r="110" fill="none" stroke="#8B5CF6" stroke-width="1" opacity="0.3"/>
  </g>
  
  <!-- Orbiting stars -->
  <g>
    <animateTransform attributeName="transform" type="rotate" 
                      values="0 100 100;360 100 100" dur="10s" repeatCount="indefinite"/>
    <!-- Star 1 -->
    <g transform="translate(190,100)">
      <circle cx="0" cy="0" r="3" fill="url(#starGradient)"/>
      <path d="M-2,-2 L2,2 M2,-2 L-2,2" stroke="#FFFFFF" stroke-width="1" opacity="0.8"/>
    </g>
    <!-- Star 2 -->
    <g transform="translate(100,10)">
      <circle cx="0" cy="0" r="2" fill="url(#starGradient)"/>
      <path d="M-1.5,-1.5 L1.5,1.5 M1.5,-1.5 L-1.5,1.5" stroke="#FFFFFF" stroke-width="0.8" opacity="0.7"/>
    </g>
  </g>
  
  <!-- Counter-rotating stars -->
  <g>
    <animateTransform attributeName="transform" type="rotate" 
                      values="360 100 100;0 100 100" dur="12s" repeatCount="indefinite"/>
    <!-- Star 3 -->
    <g transform="translate(10,100)">
      <circle cx="0" cy="0" r="2.5" fill="url(#starGradient)"/>
      <path d="M-1.8,-1.8 L1.8,1.8 M1.8,-1.8 L-1.8,1.8" stroke="#FFFFFF" stroke-width="0.9" opacity="0.6"/>
    </g>
    <!-- Star 4 -->
    <g transform="translate(100,190)">
      <circle cx="0" cy="0" r="2" fill="url(#starGradient)"/>
      <path d="M-1.5,-1.5 L1.5,1.5 M1.5,-1.5 L-1.5,1.5" stroke="#FFFFFF" stroke-width="0.8" opacity="0.5"/>
    </g>
  </g>
  
  <!-- Central highlight -->
  <circle cx="85" cy="85" r="15" fill="#FFFFFF" opacity="0.2"/>
  <circle cx="85" cy="85" r="8" fill="#38BDF8" opacity="0.4"/>
</svg>
