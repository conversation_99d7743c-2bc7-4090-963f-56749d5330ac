(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();function $h(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var uf={exports:{}},vo={},cf={exports:{}},F={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qr=Symbol.for("react.element"),Hh=Symbol.for("react.portal"),Wh=Symbol.for("react.fragment"),Gh=Symbol.for("react.strict_mode"),Kh=Symbol.for("react.profiler"),Qh=Symbol.for("react.provider"),Yh=Symbol.for("react.context"),Xh=Symbol.for("react.forward_ref"),Zh=Symbol.for("react.suspense"),qh=Symbol.for("react.memo"),Jh=Symbol.for("react.lazy"),Ya=Symbol.iterator;function em(e){return e===null||typeof e!="object"?null:(e=Ya&&e[Ya]||e["@@iterator"],typeof e=="function"?e:null)}var ff={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},df=Object.assign,pf={};function Zn(e,t,n){this.props=e,this.context=t,this.refs=pf,this.updater=n||ff}Zn.prototype.isReactComponent={};Zn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Zn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function hf(){}hf.prototype=Zn.prototype;function Al(e,t,n){this.props=e,this.context=t,this.refs=pf,this.updater=n||ff}var Dl=Al.prototype=new hf;Dl.constructor=Al;df(Dl,Zn.prototype);Dl.isPureReactComponent=!0;var Xa=Array.isArray,mf=Object.prototype.hasOwnProperty,Rl={current:null},yf={key:!0,ref:!0,__self:!0,__source:!0};function gf(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)mf.call(t,r)&&!yf.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:Qr,type:e,key:o,ref:s,props:i,_owner:Rl.current}}function tm(e,t){return{$$typeof:Qr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function _l(e){return typeof e=="object"&&e!==null&&e.$$typeof===Qr}function nm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Za=/\/+/g;function Uo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?nm(""+e.key):t.toString(36)}function Ti(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Qr:case Hh:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Uo(s,0):r,Xa(i)?(n="",e!=null&&(n=e.replace(Za,"$&/")+"/"),Ti(i,t,n,"",function(u){return u})):i!=null&&(_l(i)&&(i=tm(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Za,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",Xa(e))for(var l=0;l<e.length;l++){o=e[l];var a=r+Uo(o,l);s+=Ti(o,t,n,a,i)}else if(a=em(e),typeof a=="function")for(e=a.call(e),l=0;!(o=e.next()).done;)o=o.value,a=r+Uo(o,l++),s+=Ti(o,t,n,a,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function ii(e,t,n){if(e==null)return e;var r=[],i=0;return Ti(e,r,"","",function(o){return t.call(n,o,i++)}),r}function rm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Se={current:null},Ei={transition:null},im={ReactCurrentDispatcher:Se,ReactCurrentBatchConfig:Ei,ReactCurrentOwner:Rl};function vf(){throw Error("act(...) is not supported in production builds of React.")}F.Children={map:ii,forEach:function(e,t,n){ii(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ii(e,function(){t++}),t},toArray:function(e){return ii(e,function(t){return t})||[]},only:function(e){if(!_l(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};F.Component=Zn;F.Fragment=Wh;F.Profiler=Kh;F.PureComponent=Al;F.StrictMode=Gh;F.Suspense=Zh;F.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=im;F.act=vf;F.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=df({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Rl.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)mf.call(t,a)&&!yf.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:Qr,type:e.type,key:i,ref:o,props:r,_owner:s}};F.createContext=function(e){return e={$$typeof:Yh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Qh,_context:e},e.Consumer=e};F.createElement=gf;F.createFactory=function(e){var t=gf.bind(null,e);return t.type=e,t};F.createRef=function(){return{current:null}};F.forwardRef=function(e){return{$$typeof:Xh,render:e}};F.isValidElement=_l;F.lazy=function(e){return{$$typeof:Jh,_payload:{_status:-1,_result:e},_init:rm}};F.memo=function(e,t){return{$$typeof:qh,type:e,compare:t===void 0?null:t}};F.startTransition=function(e){var t=Ei.transition;Ei.transition={};try{e()}finally{Ei.transition=t}};F.unstable_act=vf;F.useCallback=function(e,t){return Se.current.useCallback(e,t)};F.useContext=function(e){return Se.current.useContext(e)};F.useDebugValue=function(){};F.useDeferredValue=function(e){return Se.current.useDeferredValue(e)};F.useEffect=function(e,t){return Se.current.useEffect(e,t)};F.useId=function(){return Se.current.useId()};F.useImperativeHandle=function(e,t,n){return Se.current.useImperativeHandle(e,t,n)};F.useInsertionEffect=function(e,t){return Se.current.useInsertionEffect(e,t)};F.useLayoutEffect=function(e,t){return Se.current.useLayoutEffect(e,t)};F.useMemo=function(e,t){return Se.current.useMemo(e,t)};F.useReducer=function(e,t,n){return Se.current.useReducer(e,t,n)};F.useRef=function(e){return Se.current.useRef(e)};F.useState=function(e){return Se.current.useState(e)};F.useSyncExternalStore=function(e,t,n){return Se.current.useSyncExternalStore(e,t,n)};F.useTransition=function(){return Se.current.useTransition()};F.version="18.3.1";cf.exports=F;var R=cf.exports;const Il=$h(R);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var om=R,sm=Symbol.for("react.element"),lm=Symbol.for("react.fragment"),am=Object.prototype.hasOwnProperty,um=om.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,cm={key:!0,ref:!0,__self:!0,__source:!0};function xf(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)am.call(t,r)&&!cm.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:sm,type:e,key:o,ref:s,props:i,_owner:um.current}}vo.Fragment=lm;vo.jsx=xf;vo.jsxs=xf;uf.exports=vo;var y=uf.exports,Es={},wf={exports:{}},_e={},Sf={exports:{}},kf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(j,M){var I=j.length;j.push(M);e:for(;0<I;){var D=I-1>>>1,H=j[D];if(0<i(H,M))j[D]=M,j[I]=H,I=D;else break e}}function n(j){return j.length===0?null:j[0]}function r(j){if(j.length===0)return null;var M=j[0],I=j.pop();if(I!==M){j[0]=I;e:for(var D=0,H=j.length,Qt=H>>>1;D<Qt;){var Je=2*(D+1)-1,vn=j[Je],Ve=Je+1,Yt=j[Ve];if(0>i(vn,I))Ve<H&&0>i(Yt,vn)?(j[D]=Yt,j[Ve]=I,D=Ve):(j[D]=vn,j[Je]=I,D=Je);else if(Ve<H&&0>i(Yt,I))j[D]=Yt,j[Ve]=I,D=Ve;else break e}}return M}function i(j,M){var I=j.sortIndex-M.sortIndex;return I!==0?I:j.id-M.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],c=1,f=null,d=3,g=!1,v=!1,x=!1,T=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function h(j){for(var M=n(u);M!==null;){if(M.callback===null)r(u);else if(M.startTime<=j)r(u),M.sortIndex=M.expirationTime,t(a,M);else break;M=n(u)}}function w(j){if(x=!1,h(j),!v)if(n(a)!==null)v=!0,q(S);else{var M=n(u);M!==null&&Fe(w,M.startTime-j)}}function S(j,M){v=!1,x&&(x=!1,m(P),P=-1),g=!0;var I=d;try{for(h(M),f=n(a);f!==null&&(!(f.expirationTime>M)||j&&!ie());){var D=f.callback;if(typeof D=="function"){f.callback=null,d=f.priorityLevel;var H=D(f.expirationTime<=M);M=e.unstable_now(),typeof H=="function"?f.callback=H:f===n(a)&&r(a),h(M)}else r(a);f=n(a)}if(f!==null)var Qt=!0;else{var Je=n(u);Je!==null&&Fe(w,Je.startTime-M),Qt=!1}return Qt}finally{f=null,d=I,g=!1}}var E=!1,C=null,P=-1,_=5,A=-1;function ie(){return!(e.unstable_now()-A<_)}function ae(){if(C!==null){var j=e.unstable_now();A=j;var M=!0;try{M=C(!0,j)}finally{M?ge():(E=!1,C=null)}}else E=!1}var ge;if(typeof p=="function")ge=function(){p(ae)};else if(typeof MessageChannel<"u"){var oe=new MessageChannel,wt=oe.port2;oe.port1.onmessage=ae,ge=function(){wt.postMessage(null)}}else ge=function(){T(ae,0)};function q(j){C=j,E||(E=!0,ge())}function Fe(j,M){P=T(function(){j(e.unstable_now())},M)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(j){j.callback=null},e.unstable_continueExecution=function(){v||g||(v=!0,q(S))},e.unstable_forceFrameRate=function(j){0>j||125<j?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<j?Math.floor(1e3/j):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(j){switch(d){case 1:case 2:case 3:var M=3;break;default:M=d}var I=d;d=M;try{return j()}finally{d=I}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(j,M){switch(j){case 1:case 2:case 3:case 4:case 5:break;default:j=3}var I=d;d=j;try{return M()}finally{d=I}},e.unstable_scheduleCallback=function(j,M,I){var D=e.unstable_now();switch(typeof I=="object"&&I!==null?(I=I.delay,I=typeof I=="number"&&0<I?D+I:D):I=D,j){case 1:var H=-1;break;case 2:H=250;break;case 5:H=**********;break;case 4:H=1e4;break;default:H=5e3}return H=I+H,j={id:c++,callback:M,priorityLevel:j,startTime:I,expirationTime:H,sortIndex:-1},I>D?(j.sortIndex=I,t(u,j),n(a)===null&&j===n(u)&&(x?(m(P),P=-1):x=!0,Fe(w,I-D))):(j.sortIndex=H,t(a,j),v||g||(v=!0,q(S))),j},e.unstable_shouldYield=ie,e.unstable_wrapCallback=function(j){var M=d;return function(){var I=d;d=M;try{return j.apply(this,arguments)}finally{d=I}}}})(kf);Sf.exports=kf;var fm=Sf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dm=R,De=fm;function k(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Pf=new Set,Nr={};function hn(e,t){Un(e,t),Un(e+"Capture",t)}function Un(e,t){for(Nr[e]=t,e=0;e<t.length;e++)Pf.add(t[e])}var ht=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),js=Object.prototype.hasOwnProperty,pm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,qa={},Ja={};function hm(e){return js.call(Ja,e)?!0:js.call(qa,e)?!1:pm.test(e)?Ja[e]=!0:(qa[e]=!0,!1)}function mm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function ym(e,t,n,r){if(t===null||typeof t>"u"||mm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ke(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var fe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){fe[e]=new ke(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];fe[t]=new ke(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){fe[e]=new ke(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){fe[e]=new ke(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){fe[e]=new ke(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){fe[e]=new ke(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){fe[e]=new ke(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){fe[e]=new ke(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){fe[e]=new ke(e,5,!1,e.toLowerCase(),null,!1,!1)});var Fl=/[\-:]([a-z])/g;function Ol(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Fl,Ol);fe[t]=new ke(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Fl,Ol);fe[t]=new ke(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Fl,Ol);fe[t]=new ke(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){fe[e]=new ke(e,1,!1,e.toLowerCase(),null,!1,!1)});fe.xlinkHref=new ke("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){fe[e]=new ke(e,1,!1,e.toLowerCase(),null,!0,!0)});function zl(e,t,n,r){var i=fe.hasOwnProperty(t)?fe[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(ym(t,n,i,r)&&(n=null),r||i===null?hm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var xt=dm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,oi=Symbol.for("react.element"),wn=Symbol.for("react.portal"),Sn=Symbol.for("react.fragment"),Bl=Symbol.for("react.strict_mode"),Ns=Symbol.for("react.profiler"),Cf=Symbol.for("react.provider"),Tf=Symbol.for("react.context"),bl=Symbol.for("react.forward_ref"),Vs=Symbol.for("react.suspense"),Ls=Symbol.for("react.suspense_list"),Ul=Symbol.for("react.memo"),Pt=Symbol.for("react.lazy"),Ef=Symbol.for("react.offscreen"),eu=Symbol.iterator;function er(e){return e===null||typeof e!="object"?null:(e=eu&&e[eu]||e["@@iterator"],typeof e=="function"?e:null)}var Y=Object.assign,$o;function cr(e){if($o===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);$o=t&&t[1]||""}return`
`+$o+e}var Ho=!1;function Wo(e,t){if(!e||Ho)return"";Ho=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,l=o.length-1;1<=s&&0<=l&&i[s]!==o[l];)l--;for(;1<=s&&0<=l;s--,l--)if(i[s]!==o[l]){if(s!==1||l!==1)do if(s--,l--,0>l||i[s]!==o[l]){var a=`
`+i[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{Ho=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?cr(e):""}function gm(e){switch(e.tag){case 5:return cr(e.type);case 16:return cr("Lazy");case 13:return cr("Suspense");case 19:return cr("SuspenseList");case 0:case 2:case 15:return e=Wo(e.type,!1),e;case 11:return e=Wo(e.type.render,!1),e;case 1:return e=Wo(e.type,!0),e;default:return""}}function Ms(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Sn:return"Fragment";case wn:return"Portal";case Ns:return"Profiler";case Bl:return"StrictMode";case Vs:return"Suspense";case Ls:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Tf:return(e.displayName||"Context")+".Consumer";case Cf:return(e._context.displayName||"Context")+".Provider";case bl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ul:return t=e.displayName||null,t!==null?t:Ms(e.type)||"Memo";case Pt:t=e._payload,e=e._init;try{return Ms(e(t))}catch{}}return null}function vm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ms(t);case 8:return t===Bl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Bt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function jf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function xm(e){var t=jf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function si(e){e._valueTracker||(e._valueTracker=xm(e))}function Nf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=jf(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function zi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function As(e,t){var n=t.checked;return Y({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function tu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Bt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Vf(e,t){t=t.checked,t!=null&&zl(e,"checked",t,!1)}function Ds(e,t){Vf(e,t);var n=Bt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Rs(e,t.type,n):t.hasOwnProperty("defaultValue")&&Rs(e,t.type,Bt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function nu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Rs(e,t,n){(t!=="number"||zi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var fr=Array.isArray;function In(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Bt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function _s(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(k(91));return Y({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ru(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(k(92));if(fr(n)){if(1<n.length)throw Error(k(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Bt(n)}}function Lf(e,t){var n=Bt(t.value),r=Bt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function iu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Mf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Is(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Mf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var li,Af=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(li=li||document.createElement("div"),li.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=li.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Vr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var mr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},wm=["Webkit","ms","Moz","O"];Object.keys(mr).forEach(function(e){wm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),mr[t]=mr[e]})});function Df(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||mr.hasOwnProperty(e)&&mr[e]?(""+t).trim():t+"px"}function Rf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Df(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var Sm=Y({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Fs(e,t){if(t){if(Sm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(k(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(k(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(k(61))}if(t.style!=null&&typeof t.style!="object")throw Error(k(62))}}function Os(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var zs=null;function $l(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Bs=null,Fn=null,On=null;function ou(e){if(e=Zr(e)){if(typeof Bs!="function")throw Error(k(280));var t=e.stateNode;t&&(t=Po(t),Bs(e.stateNode,e.type,t))}}function _f(e){Fn?On?On.push(e):On=[e]:Fn=e}function If(){if(Fn){var e=Fn,t=On;if(On=Fn=null,ou(e),t)for(e=0;e<t.length;e++)ou(t[e])}}function Ff(e,t){return e(t)}function Of(){}var Go=!1;function zf(e,t,n){if(Go)return e(t,n);Go=!0;try{return Ff(e,t,n)}finally{Go=!1,(Fn!==null||On!==null)&&(Of(),If())}}function Lr(e,t){var n=e.stateNode;if(n===null)return null;var r=Po(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(k(231,t,typeof n));return n}var bs=!1;if(ht)try{var tr={};Object.defineProperty(tr,"passive",{get:function(){bs=!0}}),window.addEventListener("test",tr,tr),window.removeEventListener("test",tr,tr)}catch{bs=!1}function km(e,t,n,r,i,o,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var yr=!1,Bi=null,bi=!1,Us=null,Pm={onError:function(e){yr=!0,Bi=e}};function Cm(e,t,n,r,i,o,s,l,a){yr=!1,Bi=null,km.apply(Pm,arguments)}function Tm(e,t,n,r,i,o,s,l,a){if(Cm.apply(this,arguments),yr){if(yr){var u=Bi;yr=!1,Bi=null}else throw Error(k(198));bi||(bi=!0,Us=u)}}function mn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Bf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function su(e){if(mn(e)!==e)throw Error(k(188))}function Em(e){var t=e.alternate;if(!t){if(t=mn(e),t===null)throw Error(k(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return su(i),e;if(o===r)return su(i),t;o=o.sibling}throw Error(k(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s)throw Error(k(189))}}if(n.alternate!==r)throw Error(k(190))}if(n.tag!==3)throw Error(k(188));return n.stateNode.current===n?e:t}function bf(e){return e=Em(e),e!==null?Uf(e):null}function Uf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Uf(e);if(t!==null)return t;e=e.sibling}return null}var $f=De.unstable_scheduleCallback,lu=De.unstable_cancelCallback,jm=De.unstable_shouldYield,Nm=De.unstable_requestPaint,J=De.unstable_now,Vm=De.unstable_getCurrentPriorityLevel,Hl=De.unstable_ImmediatePriority,Hf=De.unstable_UserBlockingPriority,Ui=De.unstable_NormalPriority,Lm=De.unstable_LowPriority,Wf=De.unstable_IdlePriority,xo=null,rt=null;function Mm(e){if(rt&&typeof rt.onCommitFiberRoot=="function")try{rt.onCommitFiberRoot(xo,e,void 0,(e.current.flags&128)===128)}catch{}}var Xe=Math.clz32?Math.clz32:Rm,Am=Math.log,Dm=Math.LN2;function Rm(e){return e>>>=0,e===0?32:31-(Am(e)/Dm|0)|0}var ai=64,ui=4194304;function dr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function $i(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~i;l!==0?r=dr(l):(o&=s,o!==0&&(r=dr(o)))}else s=n&~i,s!==0?r=dr(s):o!==0&&(r=dr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Xe(t),i=1<<n,r|=e[n],t&=~i;return r}function _m(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Im(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Xe(o),l=1<<s,a=i[s];a===-1?(!(l&n)||l&r)&&(i[s]=_m(l,t)):a<=t&&(e.expiredLanes|=l),o&=~l}}function $s(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Gf(){var e=ai;return ai<<=1,!(ai&4194240)&&(ai=64),e}function Ko(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Yr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Xe(t),e[t]=n}function Fm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Xe(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function Wl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Xe(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var z=0;function Kf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Qf,Gl,Yf,Xf,Zf,Hs=!1,ci=[],Lt=null,Mt=null,At=null,Mr=new Map,Ar=new Map,Et=[],Om="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function au(e,t){switch(e){case"focusin":case"focusout":Lt=null;break;case"dragenter":case"dragleave":Mt=null;break;case"mouseover":case"mouseout":At=null;break;case"pointerover":case"pointerout":Mr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ar.delete(t.pointerId)}}function nr(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=Zr(t),t!==null&&Gl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function zm(e,t,n,r,i){switch(t){case"focusin":return Lt=nr(Lt,e,t,n,r,i),!0;case"dragenter":return Mt=nr(Mt,e,t,n,r,i),!0;case"mouseover":return At=nr(At,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Mr.set(o,nr(Mr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Ar.set(o,nr(Ar.get(o)||null,e,t,n,r,i)),!0}return!1}function qf(e){var t=nn(e.target);if(t!==null){var n=mn(t);if(n!==null){if(t=n.tag,t===13){if(t=Bf(n),t!==null){e.blockedOn=t,Zf(e.priority,function(){Yf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ji(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ws(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);zs=r,n.target.dispatchEvent(r),zs=null}else return t=Zr(n),t!==null&&Gl(t),e.blockedOn=n,!1;t.shift()}return!0}function uu(e,t,n){ji(e)&&n.delete(t)}function Bm(){Hs=!1,Lt!==null&&ji(Lt)&&(Lt=null),Mt!==null&&ji(Mt)&&(Mt=null),At!==null&&ji(At)&&(At=null),Mr.forEach(uu),Ar.forEach(uu)}function rr(e,t){e.blockedOn===t&&(e.blockedOn=null,Hs||(Hs=!0,De.unstable_scheduleCallback(De.unstable_NormalPriority,Bm)))}function Dr(e){function t(i){return rr(i,e)}if(0<ci.length){rr(ci[0],e);for(var n=1;n<ci.length;n++){var r=ci[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Lt!==null&&rr(Lt,e),Mt!==null&&rr(Mt,e),At!==null&&rr(At,e),Mr.forEach(t),Ar.forEach(t),n=0;n<Et.length;n++)r=Et[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Et.length&&(n=Et[0],n.blockedOn===null);)qf(n),n.blockedOn===null&&Et.shift()}var zn=xt.ReactCurrentBatchConfig,Hi=!0;function bm(e,t,n,r){var i=z,o=zn.transition;zn.transition=null;try{z=1,Kl(e,t,n,r)}finally{z=i,zn.transition=o}}function Um(e,t,n,r){var i=z,o=zn.transition;zn.transition=null;try{z=4,Kl(e,t,n,r)}finally{z=i,zn.transition=o}}function Kl(e,t,n,r){if(Hi){var i=Ws(e,t,n,r);if(i===null)rs(e,t,r,Wi,n),au(e,r);else if(zm(i,e,t,n,r))r.stopPropagation();else if(au(e,r),t&4&&-1<Om.indexOf(e)){for(;i!==null;){var o=Zr(i);if(o!==null&&Qf(o),o=Ws(e,t,n,r),o===null&&rs(e,t,r,Wi,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else rs(e,t,r,null,n)}}var Wi=null;function Ws(e,t,n,r){if(Wi=null,e=$l(r),e=nn(e),e!==null)if(t=mn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Bf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Wi=e,null}function Jf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Vm()){case Hl:return 1;case Hf:return 4;case Ui:case Lm:return 16;case Wf:return 536870912;default:return 16}default:return 16}}var Nt=null,Ql=null,Ni=null;function ed(){if(Ni)return Ni;var e,t=Ql,n=t.length,r,i="value"in Nt?Nt.value:Nt.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Ni=i.slice(e,1<r?1-r:void 0)}function Vi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function fi(){return!0}function cu(){return!1}function Ie(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(o):o[l]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?fi:cu,this.isPropagationStopped=cu,this}return Y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=fi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=fi)},persist:function(){},isPersistent:fi}),t}var qn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Yl=Ie(qn),Xr=Y({},qn,{view:0,detail:0}),$m=Ie(Xr),Qo,Yo,ir,wo=Y({},Xr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Xl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ir&&(ir&&e.type==="mousemove"?(Qo=e.screenX-ir.screenX,Yo=e.screenY-ir.screenY):Yo=Qo=0,ir=e),Qo)},movementY:function(e){return"movementY"in e?e.movementY:Yo}}),fu=Ie(wo),Hm=Y({},wo,{dataTransfer:0}),Wm=Ie(Hm),Gm=Y({},Xr,{relatedTarget:0}),Xo=Ie(Gm),Km=Y({},qn,{animationName:0,elapsedTime:0,pseudoElement:0}),Qm=Ie(Km),Ym=Y({},qn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Xm=Ie(Ym),Zm=Y({},qn,{data:0}),du=Ie(Zm),qm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},e0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function t0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=e0[e])?!!t[e]:!1}function Xl(){return t0}var n0=Y({},Xr,{key:function(e){if(e.key){var t=qm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Vi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Jm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Xl,charCode:function(e){return e.type==="keypress"?Vi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Vi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),r0=Ie(n0),i0=Y({},wo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pu=Ie(i0),o0=Y({},Xr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Xl}),s0=Ie(o0),l0=Y({},qn,{propertyName:0,elapsedTime:0,pseudoElement:0}),a0=Ie(l0),u0=Y({},wo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),c0=Ie(u0),f0=[9,13,27,32],Zl=ht&&"CompositionEvent"in window,gr=null;ht&&"documentMode"in document&&(gr=document.documentMode);var d0=ht&&"TextEvent"in window&&!gr,td=ht&&(!Zl||gr&&8<gr&&11>=gr),hu=String.fromCharCode(32),mu=!1;function nd(e,t){switch(e){case"keyup":return f0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function rd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var kn=!1;function p0(e,t){switch(e){case"compositionend":return rd(t);case"keypress":return t.which!==32?null:(mu=!0,hu);case"textInput":return e=t.data,e===hu&&mu?null:e;default:return null}}function h0(e,t){if(kn)return e==="compositionend"||!Zl&&nd(e,t)?(e=ed(),Ni=Ql=Nt=null,kn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return td&&t.locale!=="ko"?null:t.data;default:return null}}var m0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!m0[e.type]:t==="textarea"}function id(e,t,n,r){_f(r),t=Gi(t,"onChange"),0<t.length&&(n=new Yl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var vr=null,Rr=null;function y0(e){md(e,0)}function So(e){var t=Tn(e);if(Nf(t))return e}function g0(e,t){if(e==="change")return t}var od=!1;if(ht){var Zo;if(ht){var qo="oninput"in document;if(!qo){var gu=document.createElement("div");gu.setAttribute("oninput","return;"),qo=typeof gu.oninput=="function"}Zo=qo}else Zo=!1;od=Zo&&(!document.documentMode||9<document.documentMode)}function vu(){vr&&(vr.detachEvent("onpropertychange",sd),Rr=vr=null)}function sd(e){if(e.propertyName==="value"&&So(Rr)){var t=[];id(t,Rr,e,$l(e)),zf(y0,t)}}function v0(e,t,n){e==="focusin"?(vu(),vr=t,Rr=n,vr.attachEvent("onpropertychange",sd)):e==="focusout"&&vu()}function x0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return So(Rr)}function w0(e,t){if(e==="click")return So(t)}function S0(e,t){if(e==="input"||e==="change")return So(t)}function k0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var qe=typeof Object.is=="function"?Object.is:k0;function _r(e,t){if(qe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!js.call(t,i)||!qe(e[i],t[i]))return!1}return!0}function xu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function wu(e,t){var n=xu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=xu(n)}}function ld(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ld(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ad(){for(var e=window,t=zi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=zi(e.document)}return t}function ql(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function P0(e){var t=ad(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ld(n.ownerDocument.documentElement,n)){if(r!==null&&ql(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=wu(n,o);var s=wu(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var C0=ht&&"documentMode"in document&&11>=document.documentMode,Pn=null,Gs=null,xr=null,Ks=!1;function Su(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ks||Pn==null||Pn!==zi(r)||(r=Pn,"selectionStart"in r&&ql(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),xr&&_r(xr,r)||(xr=r,r=Gi(Gs,"onSelect"),0<r.length&&(t=new Yl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Pn)))}function di(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Cn={animationend:di("Animation","AnimationEnd"),animationiteration:di("Animation","AnimationIteration"),animationstart:di("Animation","AnimationStart"),transitionend:di("Transition","TransitionEnd")},Jo={},ud={};ht&&(ud=document.createElement("div").style,"AnimationEvent"in window||(delete Cn.animationend.animation,delete Cn.animationiteration.animation,delete Cn.animationstart.animation),"TransitionEvent"in window||delete Cn.transitionend.transition);function ko(e){if(Jo[e])return Jo[e];if(!Cn[e])return e;var t=Cn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ud)return Jo[e]=t[n];return e}var cd=ko("animationend"),fd=ko("animationiteration"),dd=ko("animationstart"),pd=ko("transitionend"),hd=new Map,ku="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ht(e,t){hd.set(e,t),hn(t,[e])}for(var es=0;es<ku.length;es++){var ts=ku[es],T0=ts.toLowerCase(),E0=ts[0].toUpperCase()+ts.slice(1);Ht(T0,"on"+E0)}Ht(cd,"onAnimationEnd");Ht(fd,"onAnimationIteration");Ht(dd,"onAnimationStart");Ht("dblclick","onDoubleClick");Ht("focusin","onFocus");Ht("focusout","onBlur");Ht(pd,"onTransitionEnd");Un("onMouseEnter",["mouseout","mouseover"]);Un("onMouseLeave",["mouseout","mouseover"]);Un("onPointerEnter",["pointerout","pointerover"]);Un("onPointerLeave",["pointerout","pointerover"]);hn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));hn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));hn("onBeforeInput",["compositionend","keypress","textInput","paste"]);hn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));hn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));hn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var pr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),j0=new Set("cancel close invalid load scroll toggle".split(" ").concat(pr));function Pu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Tm(r,t,void 0,e),e.currentTarget=null}function md(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==o&&i.isPropagationStopped())break e;Pu(i,l,u),o=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==o&&i.isPropagationStopped())break e;Pu(i,l,u),o=a}}}if(bi)throw e=Us,bi=!1,Us=null,e}function b(e,t){var n=t[qs];n===void 0&&(n=t[qs]=new Set);var r=e+"__bubble";n.has(r)||(yd(t,e,2,!1),n.add(r))}function ns(e,t,n){var r=0;t&&(r|=4),yd(n,e,r,t)}var pi="_reactListening"+Math.random().toString(36).slice(2);function Ir(e){if(!e[pi]){e[pi]=!0,Pf.forEach(function(n){n!=="selectionchange"&&(j0.has(n)||ns(n,!1,e),ns(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[pi]||(t[pi]=!0,ns("selectionchange",!1,t))}}function yd(e,t,n,r){switch(Jf(t)){case 1:var i=bm;break;case 4:i=Um;break;default:i=Kl}n=i.bind(null,t,n,e),i=void 0,!bs||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function rs(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;s=s.return}for(;l!==null;){if(s=nn(l),s===null)return;if(a=s.tag,a===5||a===6){r=o=s;continue e}l=l.parentNode}}r=r.return}zf(function(){var u=o,c=$l(n),f=[];e:{var d=hd.get(e);if(d!==void 0){var g=Yl,v=e;switch(e){case"keypress":if(Vi(n)===0)break e;case"keydown":case"keyup":g=r0;break;case"focusin":v="focus",g=Xo;break;case"focusout":v="blur",g=Xo;break;case"beforeblur":case"afterblur":g=Xo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=fu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Wm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=s0;break;case cd:case fd:case dd:g=Qm;break;case pd:g=a0;break;case"scroll":g=$m;break;case"wheel":g=c0;break;case"copy":case"cut":case"paste":g=Xm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=pu}var x=(t&4)!==0,T=!x&&e==="scroll",m=x?d!==null?d+"Capture":null:d;x=[];for(var p=u,h;p!==null;){h=p;var w=h.stateNode;if(h.tag===5&&w!==null&&(h=w,m!==null&&(w=Lr(p,m),w!=null&&x.push(Fr(p,w,h)))),T)break;p=p.return}0<x.length&&(d=new g(d,v,null,n,c),f.push({event:d,listeners:x}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",d&&n!==zs&&(v=n.relatedTarget||n.fromElement)&&(nn(v)||v[mt]))break e;if((g||d)&&(d=c.window===c?c:(d=c.ownerDocument)?d.defaultView||d.parentWindow:window,g?(v=n.relatedTarget||n.toElement,g=u,v=v?nn(v):null,v!==null&&(T=mn(v),v!==T||v.tag!==5&&v.tag!==6)&&(v=null)):(g=null,v=u),g!==v)){if(x=fu,w="onMouseLeave",m="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(x=pu,w="onPointerLeave",m="onPointerEnter",p="pointer"),T=g==null?d:Tn(g),h=v==null?d:Tn(v),d=new x(w,p+"leave",g,n,c),d.target=T,d.relatedTarget=h,w=null,nn(c)===u&&(x=new x(m,p+"enter",v,n,c),x.target=h,x.relatedTarget=T,w=x),T=w,g&&v)t:{for(x=g,m=v,p=0,h=x;h;h=xn(h))p++;for(h=0,w=m;w;w=xn(w))h++;for(;0<p-h;)x=xn(x),p--;for(;0<h-p;)m=xn(m),h--;for(;p--;){if(x===m||m!==null&&x===m.alternate)break t;x=xn(x),m=xn(m)}x=null}else x=null;g!==null&&Cu(f,d,g,x,!1),v!==null&&T!==null&&Cu(f,T,v,x,!0)}}e:{if(d=u?Tn(u):window,g=d.nodeName&&d.nodeName.toLowerCase(),g==="select"||g==="input"&&d.type==="file")var S=g0;else if(yu(d))if(od)S=S0;else{S=x0;var E=v0}else(g=d.nodeName)&&g.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(S=w0);if(S&&(S=S(e,u))){id(f,S,n,c);break e}E&&E(e,d,u),e==="focusout"&&(E=d._wrapperState)&&E.controlled&&d.type==="number"&&Rs(d,"number",d.value)}switch(E=u?Tn(u):window,e){case"focusin":(yu(E)||E.contentEditable==="true")&&(Pn=E,Gs=u,xr=null);break;case"focusout":xr=Gs=Pn=null;break;case"mousedown":Ks=!0;break;case"contextmenu":case"mouseup":case"dragend":Ks=!1,Su(f,n,c);break;case"selectionchange":if(C0)break;case"keydown":case"keyup":Su(f,n,c)}var C;if(Zl)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else kn?nd(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(td&&n.locale!=="ko"&&(kn||P!=="onCompositionStart"?P==="onCompositionEnd"&&kn&&(C=ed()):(Nt=c,Ql="value"in Nt?Nt.value:Nt.textContent,kn=!0)),E=Gi(u,P),0<E.length&&(P=new du(P,e,null,n,c),f.push({event:P,listeners:E}),C?P.data=C:(C=rd(n),C!==null&&(P.data=C)))),(C=d0?p0(e,n):h0(e,n))&&(u=Gi(u,"onBeforeInput"),0<u.length&&(c=new du("onBeforeInput","beforeinput",null,n,c),f.push({event:c,listeners:u}),c.data=C))}md(f,t)})}function Fr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Gi(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Lr(e,n),o!=null&&r.unshift(Fr(e,o,i)),o=Lr(e,t),o!=null&&r.push(Fr(e,o,i))),e=e.return}return r}function xn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Cu(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=Lr(n,o),a!=null&&s.unshift(Fr(n,a,l))):i||(a=Lr(n,o),a!=null&&s.push(Fr(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var N0=/\r\n?/g,V0=/\u0000|\uFFFD/g;function Tu(e){return(typeof e=="string"?e:""+e).replace(N0,`
`).replace(V0,"")}function hi(e,t,n){if(t=Tu(t),Tu(e)!==t&&n)throw Error(k(425))}function Ki(){}var Qs=null,Ys=null;function Xs(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Zs=typeof setTimeout=="function"?setTimeout:void 0,L0=typeof clearTimeout=="function"?clearTimeout:void 0,Eu=typeof Promise=="function"?Promise:void 0,M0=typeof queueMicrotask=="function"?queueMicrotask:typeof Eu<"u"?function(e){return Eu.resolve(null).then(e).catch(A0)}:Zs;function A0(e){setTimeout(function(){throw e})}function is(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Dr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Dr(t)}function Dt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ju(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Jn=Math.random().toString(36).slice(2),nt="__reactFiber$"+Jn,Or="__reactProps$"+Jn,mt="__reactContainer$"+Jn,qs="__reactEvents$"+Jn,D0="__reactListeners$"+Jn,R0="__reactHandles$"+Jn;function nn(e){var t=e[nt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mt]||n[nt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ju(e);e!==null;){if(n=e[nt])return n;e=ju(e)}return t}e=n,n=e.parentNode}return null}function Zr(e){return e=e[nt]||e[mt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Tn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(k(33))}function Po(e){return e[Or]||null}var Js=[],En=-1;function Wt(e){return{current:e}}function U(e){0>En||(e.current=Js[En],Js[En]=null,En--)}function B(e,t){En++,Js[En]=e.current,e.current=t}var bt={},ye=Wt(bt),Te=Wt(!1),un=bt;function $n(e,t){var n=e.type.contextTypes;if(!n)return bt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Ee(e){return e=e.childContextTypes,e!=null}function Qi(){U(Te),U(ye)}function Nu(e,t,n){if(ye.current!==bt)throw Error(k(168));B(ye,t),B(Te,n)}function gd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(k(108,vm(e)||"Unknown",i));return Y({},n,r)}function Yi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||bt,un=ye.current,B(ye,e),B(Te,Te.current),!0}function Vu(e,t,n){var r=e.stateNode;if(!r)throw Error(k(169));n?(e=gd(e,t,un),r.__reactInternalMemoizedMergedChildContext=e,U(Te),U(ye),B(ye,e)):U(Te),B(Te,n)}var lt=null,Co=!1,os=!1;function vd(e){lt===null?lt=[e]:lt.push(e)}function _0(e){Co=!0,vd(e)}function Gt(){if(!os&&lt!==null){os=!0;var e=0,t=z;try{var n=lt;for(z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}lt=null,Co=!1}catch(i){throw lt!==null&&(lt=lt.slice(e+1)),$f(Hl,Gt),i}finally{z=t,os=!1}}return null}var jn=[],Nn=0,Xi=null,Zi=0,Be=[],be=0,cn=null,at=1,ut="";function qt(e,t){jn[Nn++]=Zi,jn[Nn++]=Xi,Xi=e,Zi=t}function xd(e,t,n){Be[be++]=at,Be[be++]=ut,Be[be++]=cn,cn=e;var r=at;e=ut;var i=32-Xe(r)-1;r&=~(1<<i),n+=1;var o=32-Xe(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,at=1<<32-Xe(t)+i|n<<i|r,ut=o+e}else at=1<<o|n<<i|r,ut=e}function Jl(e){e.return!==null&&(qt(e,1),xd(e,1,0))}function ea(e){for(;e===Xi;)Xi=jn[--Nn],jn[Nn]=null,Zi=jn[--Nn],jn[Nn]=null;for(;e===cn;)cn=Be[--be],Be[be]=null,ut=Be[--be],Be[be]=null,at=Be[--be],Be[be]=null}var Ae=null,Me=null,W=!1,Ye=null;function wd(e,t){var n=Ue(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Lu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ae=e,Me=Dt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ae=e,Me=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=cn!==null?{id:at,overflow:ut}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ue(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ae=e,Me=null,!0):!1;default:return!1}}function el(e){return(e.mode&1)!==0&&(e.flags&128)===0}function tl(e){if(W){var t=Me;if(t){var n=t;if(!Lu(e,t)){if(el(e))throw Error(k(418));t=Dt(n.nextSibling);var r=Ae;t&&Lu(e,t)?wd(r,n):(e.flags=e.flags&-4097|2,W=!1,Ae=e)}}else{if(el(e))throw Error(k(418));e.flags=e.flags&-4097|2,W=!1,Ae=e}}}function Mu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ae=e}function mi(e){if(e!==Ae)return!1;if(!W)return Mu(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Xs(e.type,e.memoizedProps)),t&&(t=Me)){if(el(e))throw Sd(),Error(k(418));for(;t;)wd(e,t),t=Dt(t.nextSibling)}if(Mu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(k(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Me=Dt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Me=null}}else Me=Ae?Dt(e.stateNode.nextSibling):null;return!0}function Sd(){for(var e=Me;e;)e=Dt(e.nextSibling)}function Hn(){Me=Ae=null,W=!1}function ta(e){Ye===null?Ye=[e]:Ye.push(e)}var I0=xt.ReactCurrentBatchConfig;function or(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(k(309));var r=n.stateNode}if(!r)throw Error(k(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var l=i.refs;s===null?delete l[o]:l[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(k(284));if(!n._owner)throw Error(k(290,e))}return e}function yi(e,t){throw e=Object.prototype.toString.call(t),Error(k(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Au(e){var t=e._init;return t(e._payload)}function kd(e){function t(m,p){if(e){var h=m.deletions;h===null?(m.deletions=[p],m.flags|=16):h.push(p)}}function n(m,p){if(!e)return null;for(;p!==null;)t(m,p),p=p.sibling;return null}function r(m,p){for(m=new Map;p!==null;)p.key!==null?m.set(p.key,p):m.set(p.index,p),p=p.sibling;return m}function i(m,p){return m=Ft(m,p),m.index=0,m.sibling=null,m}function o(m,p,h){return m.index=h,e?(h=m.alternate,h!==null?(h=h.index,h<p?(m.flags|=2,p):h):(m.flags|=2,p)):(m.flags|=1048576,p)}function s(m){return e&&m.alternate===null&&(m.flags|=2),m}function l(m,p,h,w){return p===null||p.tag!==6?(p=ds(h,m.mode,w),p.return=m,p):(p=i(p,h),p.return=m,p)}function a(m,p,h,w){var S=h.type;return S===Sn?c(m,p,h.props.children,w,h.key):p!==null&&(p.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Pt&&Au(S)===p.type)?(w=i(p,h.props),w.ref=or(m,p,h),w.return=m,w):(w=Ii(h.type,h.key,h.props,null,m.mode,w),w.ref=or(m,p,h),w.return=m,w)}function u(m,p,h,w){return p===null||p.tag!==4||p.stateNode.containerInfo!==h.containerInfo||p.stateNode.implementation!==h.implementation?(p=ps(h,m.mode,w),p.return=m,p):(p=i(p,h.children||[]),p.return=m,p)}function c(m,p,h,w,S){return p===null||p.tag!==7?(p=an(h,m.mode,w,S),p.return=m,p):(p=i(p,h),p.return=m,p)}function f(m,p,h){if(typeof p=="string"&&p!==""||typeof p=="number")return p=ds(""+p,m.mode,h),p.return=m,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case oi:return h=Ii(p.type,p.key,p.props,null,m.mode,h),h.ref=or(m,null,p),h.return=m,h;case wn:return p=ps(p,m.mode,h),p.return=m,p;case Pt:var w=p._init;return f(m,w(p._payload),h)}if(fr(p)||er(p))return p=an(p,m.mode,h,null),p.return=m,p;yi(m,p)}return null}function d(m,p,h,w){var S=p!==null?p.key:null;if(typeof h=="string"&&h!==""||typeof h=="number")return S!==null?null:l(m,p,""+h,w);if(typeof h=="object"&&h!==null){switch(h.$$typeof){case oi:return h.key===S?a(m,p,h,w):null;case wn:return h.key===S?u(m,p,h,w):null;case Pt:return S=h._init,d(m,p,S(h._payload),w)}if(fr(h)||er(h))return S!==null?null:c(m,p,h,w,null);yi(m,h)}return null}function g(m,p,h,w,S){if(typeof w=="string"&&w!==""||typeof w=="number")return m=m.get(h)||null,l(p,m,""+w,S);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case oi:return m=m.get(w.key===null?h:w.key)||null,a(p,m,w,S);case wn:return m=m.get(w.key===null?h:w.key)||null,u(p,m,w,S);case Pt:var E=w._init;return g(m,p,h,E(w._payload),S)}if(fr(w)||er(w))return m=m.get(h)||null,c(p,m,w,S,null);yi(p,w)}return null}function v(m,p,h,w){for(var S=null,E=null,C=p,P=p=0,_=null;C!==null&&P<h.length;P++){C.index>P?(_=C,C=null):_=C.sibling;var A=d(m,C,h[P],w);if(A===null){C===null&&(C=_);break}e&&C&&A.alternate===null&&t(m,C),p=o(A,p,P),E===null?S=A:E.sibling=A,E=A,C=_}if(P===h.length)return n(m,C),W&&qt(m,P),S;if(C===null){for(;P<h.length;P++)C=f(m,h[P],w),C!==null&&(p=o(C,p,P),E===null?S=C:E.sibling=C,E=C);return W&&qt(m,P),S}for(C=r(m,C);P<h.length;P++)_=g(C,m,P,h[P],w),_!==null&&(e&&_.alternate!==null&&C.delete(_.key===null?P:_.key),p=o(_,p,P),E===null?S=_:E.sibling=_,E=_);return e&&C.forEach(function(ie){return t(m,ie)}),W&&qt(m,P),S}function x(m,p,h,w){var S=er(h);if(typeof S!="function")throw Error(k(150));if(h=S.call(h),h==null)throw Error(k(151));for(var E=S=null,C=p,P=p=0,_=null,A=h.next();C!==null&&!A.done;P++,A=h.next()){C.index>P?(_=C,C=null):_=C.sibling;var ie=d(m,C,A.value,w);if(ie===null){C===null&&(C=_);break}e&&C&&ie.alternate===null&&t(m,C),p=o(ie,p,P),E===null?S=ie:E.sibling=ie,E=ie,C=_}if(A.done)return n(m,C),W&&qt(m,P),S;if(C===null){for(;!A.done;P++,A=h.next())A=f(m,A.value,w),A!==null&&(p=o(A,p,P),E===null?S=A:E.sibling=A,E=A);return W&&qt(m,P),S}for(C=r(m,C);!A.done;P++,A=h.next())A=g(C,m,P,A.value,w),A!==null&&(e&&A.alternate!==null&&C.delete(A.key===null?P:A.key),p=o(A,p,P),E===null?S=A:E.sibling=A,E=A);return e&&C.forEach(function(ae){return t(m,ae)}),W&&qt(m,P),S}function T(m,p,h,w){if(typeof h=="object"&&h!==null&&h.type===Sn&&h.key===null&&(h=h.props.children),typeof h=="object"&&h!==null){switch(h.$$typeof){case oi:e:{for(var S=h.key,E=p;E!==null;){if(E.key===S){if(S=h.type,S===Sn){if(E.tag===7){n(m,E.sibling),p=i(E,h.props.children),p.return=m,m=p;break e}}else if(E.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Pt&&Au(S)===E.type){n(m,E.sibling),p=i(E,h.props),p.ref=or(m,E,h),p.return=m,m=p;break e}n(m,E);break}else t(m,E);E=E.sibling}h.type===Sn?(p=an(h.props.children,m.mode,w,h.key),p.return=m,m=p):(w=Ii(h.type,h.key,h.props,null,m.mode,w),w.ref=or(m,p,h),w.return=m,m=w)}return s(m);case wn:e:{for(E=h.key;p!==null;){if(p.key===E)if(p.tag===4&&p.stateNode.containerInfo===h.containerInfo&&p.stateNode.implementation===h.implementation){n(m,p.sibling),p=i(p,h.children||[]),p.return=m,m=p;break e}else{n(m,p);break}else t(m,p);p=p.sibling}p=ps(h,m.mode,w),p.return=m,m=p}return s(m);case Pt:return E=h._init,T(m,p,E(h._payload),w)}if(fr(h))return v(m,p,h,w);if(er(h))return x(m,p,h,w);yi(m,h)}return typeof h=="string"&&h!==""||typeof h=="number"?(h=""+h,p!==null&&p.tag===6?(n(m,p.sibling),p=i(p,h),p.return=m,m=p):(n(m,p),p=ds(h,m.mode,w),p.return=m,m=p),s(m)):n(m,p)}return T}var Wn=kd(!0),Pd=kd(!1),qi=Wt(null),Ji=null,Vn=null,na=null;function ra(){na=Vn=Ji=null}function ia(e){var t=qi.current;U(qi),e._currentValue=t}function nl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Bn(e,t){Ji=e,na=Vn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ce=!0),e.firstContext=null)}function He(e){var t=e._currentValue;if(na!==e)if(e={context:e,memoizedValue:t,next:null},Vn===null){if(Ji===null)throw Error(k(308));Vn=e,Ji.dependencies={lanes:0,firstContext:e}}else Vn=Vn.next=e;return t}var rn=null;function oa(e){rn===null?rn=[e]:rn.push(e)}function Cd(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,oa(t)):(n.next=i.next,i.next=n),t.interleaved=n,yt(e,r)}function yt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Ct=!1;function sa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Td(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ft(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Rt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,O&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,yt(e,n)}return i=r.interleaved,i===null?(t.next=t,oa(r)):(t.next=i.next,i.next=t),r.interleaved=t,yt(e,n)}function Li(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Wl(e,n)}}function Du(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function eo(e,t,n,r){var i=e.updateQueue;Ct=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?o=u:s.next=u,s=a;var c=e.alternate;c!==null&&(c=c.updateQueue,l=c.lastBaseUpdate,l!==s&&(l===null?c.firstBaseUpdate=u:l.next=u,c.lastBaseUpdate=a))}if(o!==null){var f=i.baseState;s=0,c=u=a=null,l=o;do{var d=l.lane,g=l.eventTime;if((r&d)===d){c!==null&&(c=c.next={eventTime:g,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var v=e,x=l;switch(d=t,g=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){f=v.call(g,f,d);break e}f=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,d=typeof v=="function"?v.call(g,f,d):v,d==null)break e;f=Y({},f,d);break e;case 2:Ct=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,d=i.effects,d===null?i.effects=[l]:d.push(l))}else g={eventTime:g,lane:d,tag:l.tag,payload:l.payload,callback:l.callback,next:null},c===null?(u=c=g,a=f):c=c.next=g,s|=d;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;d=l,l=d.next,d.next=null,i.lastBaseUpdate=d,i.shared.pending=null}}while(1);if(c===null&&(a=f),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);dn|=s,e.lanes=s,e.memoizedState=f}}function Ru(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(k(191,i));i.call(r)}}}var qr={},it=Wt(qr),zr=Wt(qr),Br=Wt(qr);function on(e){if(e===qr)throw Error(k(174));return e}function la(e,t){switch(B(Br,t),B(zr,e),B(it,qr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Is(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Is(t,e)}U(it),B(it,t)}function Gn(){U(it),U(zr),U(Br)}function Ed(e){on(Br.current);var t=on(it.current),n=Is(t,e.type);t!==n&&(B(zr,e),B(it,n))}function aa(e){zr.current===e&&(U(it),U(zr))}var G=Wt(0);function to(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ss=[];function ua(){for(var e=0;e<ss.length;e++)ss[e]._workInProgressVersionPrimary=null;ss.length=0}var Mi=xt.ReactCurrentDispatcher,ls=xt.ReactCurrentBatchConfig,fn=0,Q=null,ne=null,se=null,no=!1,wr=!1,br=0,F0=0;function de(){throw Error(k(321))}function ca(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!qe(e[n],t[n]))return!1;return!0}function fa(e,t,n,r,i,o){if(fn=o,Q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Mi.current=e===null||e.memoizedState===null?b0:U0,e=n(r,i),wr){o=0;do{if(wr=!1,br=0,25<=o)throw Error(k(301));o+=1,se=ne=null,t.updateQueue=null,Mi.current=$0,e=n(r,i)}while(wr)}if(Mi.current=ro,t=ne!==null&&ne.next!==null,fn=0,se=ne=Q=null,no=!1,t)throw Error(k(300));return e}function da(){var e=br!==0;return br=0,e}function tt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return se===null?Q.memoizedState=se=e:se=se.next=e,se}function We(){if(ne===null){var e=Q.alternate;e=e!==null?e.memoizedState:null}else e=ne.next;var t=se===null?Q.memoizedState:se.next;if(t!==null)se=t,ne=e;else{if(e===null)throw Error(k(310));ne=e,e={memoizedState:ne.memoizedState,baseState:ne.baseState,baseQueue:ne.baseQueue,queue:ne.queue,next:null},se===null?Q.memoizedState=se=e:se=se.next=e}return se}function Ur(e,t){return typeof t=="function"?t(e):t}function as(e){var t=We(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=ne,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var l=s=null,a=null,u=o;do{var c=u.lane;if((fn&c)===c)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=f,s=r):a=a.next=f,Q.lanes|=c,dn|=c}u=u.next}while(u!==null&&u!==o);a===null?s=r:a.next=l,qe(r,t.memoizedState)||(Ce=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,Q.lanes|=o,dn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function us(e){var t=We(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);qe(o,t.memoizedState)||(Ce=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function jd(){}function Nd(e,t){var n=Q,r=We(),i=t(),o=!qe(r.memoizedState,i);if(o&&(r.memoizedState=i,Ce=!0),r=r.queue,pa(Md.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||se!==null&&se.memoizedState.tag&1){if(n.flags|=2048,$r(9,Ld.bind(null,n,r,i,t),void 0,null),le===null)throw Error(k(349));fn&30||Vd(n,t,i)}return i}function Vd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Ld(e,t,n,r){t.value=n,t.getSnapshot=r,Ad(t)&&Dd(e)}function Md(e,t,n){return n(function(){Ad(t)&&Dd(e)})}function Ad(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!qe(e,n)}catch{return!0}}function Dd(e){var t=yt(e,1);t!==null&&Ze(t,e,1,-1)}function _u(e){var t=tt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ur,lastRenderedState:e},t.queue=e,e=e.dispatch=B0.bind(null,Q,e),[t.memoizedState,e]}function $r(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Rd(){return We().memoizedState}function Ai(e,t,n,r){var i=tt();Q.flags|=e,i.memoizedState=$r(1|t,n,void 0,r===void 0?null:r)}function To(e,t,n,r){var i=We();r=r===void 0?null:r;var o=void 0;if(ne!==null){var s=ne.memoizedState;if(o=s.destroy,r!==null&&ca(r,s.deps)){i.memoizedState=$r(t,n,o,r);return}}Q.flags|=e,i.memoizedState=$r(1|t,n,o,r)}function Iu(e,t){return Ai(8390656,8,e,t)}function pa(e,t){return To(2048,8,e,t)}function _d(e,t){return To(4,2,e,t)}function Id(e,t){return To(4,4,e,t)}function Fd(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Od(e,t,n){return n=n!=null?n.concat([e]):null,To(4,4,Fd.bind(null,t,e),n)}function ha(){}function zd(e,t){var n=We();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ca(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Bd(e,t){var n=We();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ca(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function bd(e,t,n){return fn&21?(qe(n,t)||(n=Gf(),Q.lanes|=n,dn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ce=!0),e.memoizedState=n)}function O0(e,t){var n=z;z=n!==0&&4>n?n:4,e(!0);var r=ls.transition;ls.transition={};try{e(!1),t()}finally{z=n,ls.transition=r}}function Ud(){return We().memoizedState}function z0(e,t,n){var r=It(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},$d(e))Hd(t,n);else if(n=Cd(e,t,n,r),n!==null){var i=we();Ze(n,e,r,i),Wd(n,t,r)}}function B0(e,t,n){var r=It(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if($d(e))Hd(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,l=o(s,n);if(i.hasEagerState=!0,i.eagerState=l,qe(l,s)){var a=t.interleaved;a===null?(i.next=i,oa(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=Cd(e,t,i,r),n!==null&&(i=we(),Ze(n,e,r,i),Wd(n,t,r))}}function $d(e){var t=e.alternate;return e===Q||t!==null&&t===Q}function Hd(e,t){wr=no=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Wd(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Wl(e,n)}}var ro={readContext:He,useCallback:de,useContext:de,useEffect:de,useImperativeHandle:de,useInsertionEffect:de,useLayoutEffect:de,useMemo:de,useReducer:de,useRef:de,useState:de,useDebugValue:de,useDeferredValue:de,useTransition:de,useMutableSource:de,useSyncExternalStore:de,useId:de,unstable_isNewReconciler:!1},b0={readContext:He,useCallback:function(e,t){return tt().memoizedState=[e,t===void 0?null:t],e},useContext:He,useEffect:Iu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Ai(4194308,4,Fd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ai(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ai(4,2,e,t)},useMemo:function(e,t){var n=tt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=tt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=z0.bind(null,Q,e),[r.memoizedState,e]},useRef:function(e){var t=tt();return e={current:e},t.memoizedState=e},useState:_u,useDebugValue:ha,useDeferredValue:function(e){return tt().memoizedState=e},useTransition:function(){var e=_u(!1),t=e[0];return e=O0.bind(null,e[1]),tt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Q,i=tt();if(W){if(n===void 0)throw Error(k(407));n=n()}else{if(n=t(),le===null)throw Error(k(349));fn&30||Vd(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,Iu(Md.bind(null,r,o,e),[e]),r.flags|=2048,$r(9,Ld.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=tt(),t=le.identifierPrefix;if(W){var n=ut,r=at;n=(r&~(1<<32-Xe(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=br++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=F0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},U0={readContext:He,useCallback:zd,useContext:He,useEffect:pa,useImperativeHandle:Od,useInsertionEffect:_d,useLayoutEffect:Id,useMemo:Bd,useReducer:as,useRef:Rd,useState:function(){return as(Ur)},useDebugValue:ha,useDeferredValue:function(e){var t=We();return bd(t,ne.memoizedState,e)},useTransition:function(){var e=as(Ur)[0],t=We().memoizedState;return[e,t]},useMutableSource:jd,useSyncExternalStore:Nd,useId:Ud,unstable_isNewReconciler:!1},$0={readContext:He,useCallback:zd,useContext:He,useEffect:pa,useImperativeHandle:Od,useInsertionEffect:_d,useLayoutEffect:Id,useMemo:Bd,useReducer:us,useRef:Rd,useState:function(){return us(Ur)},useDebugValue:ha,useDeferredValue:function(e){var t=We();return ne===null?t.memoizedState=e:bd(t,ne.memoizedState,e)},useTransition:function(){var e=us(Ur)[0],t=We().memoizedState;return[e,t]},useMutableSource:jd,useSyncExternalStore:Nd,useId:Ud,unstable_isNewReconciler:!1};function Ke(e,t){if(e&&e.defaultProps){t=Y({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function rl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Eo={isMounted:function(e){return(e=e._reactInternals)?mn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=we(),i=It(e),o=ft(r,i);o.payload=t,n!=null&&(o.callback=n),t=Rt(e,o,i),t!==null&&(Ze(t,e,i,r),Li(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=we(),i=It(e),o=ft(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Rt(e,o,i),t!==null&&(Ze(t,e,i,r),Li(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=we(),r=It(e),i=ft(n,r);i.tag=2,t!=null&&(i.callback=t),t=Rt(e,i,r),t!==null&&(Ze(t,e,r,n),Li(t,e,r))}};function Fu(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!_r(n,r)||!_r(i,o):!0}function Gd(e,t,n){var r=!1,i=bt,o=t.contextType;return typeof o=="object"&&o!==null?o=He(o):(i=Ee(t)?un:ye.current,r=t.contextTypes,o=(r=r!=null)?$n(e,i):bt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Eo,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function Ou(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Eo.enqueueReplaceState(t,t.state,null)}function il(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},sa(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=He(o):(o=Ee(t)?un:ye.current,i.context=$n(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(rl(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Eo.enqueueReplaceState(i,i.state,null),eo(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Kn(e,t){try{var n="",r=t;do n+=gm(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function cs(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ol(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var H0=typeof WeakMap=="function"?WeakMap:Map;function Kd(e,t,n){n=ft(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){oo||(oo=!0,ml=r),ol(e,t)},n}function Qd(e,t,n){n=ft(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){ol(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){ol(e,t),typeof r!="function"&&(_t===null?_t=new Set([this]):_t.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function zu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new H0;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=iy.bind(null,e,t,n),t.then(e,e))}function Bu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function bu(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ft(-1,1),t.tag=2,Rt(n,t,1))),n.lanes|=1),e)}var W0=xt.ReactCurrentOwner,Ce=!1;function xe(e,t,n,r){t.child=e===null?Pd(t,null,n,r):Wn(t,e.child,n,r)}function Uu(e,t,n,r,i){n=n.render;var o=t.ref;return Bn(t,i),r=fa(e,t,n,r,o,i),n=da(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gt(e,t,i)):(W&&n&&Jl(t),t.flags|=1,xe(e,t,r,i),t.child)}function $u(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!ka(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Yd(e,t,o,r,i)):(e=Ii(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:_r,n(s,r)&&e.ref===t.ref)return gt(e,t,i)}return t.flags|=1,e=Ft(o,r),e.ref=t.ref,e.return=t,t.child=e}function Yd(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(_r(o,r)&&e.ref===t.ref)if(Ce=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Ce=!0);else return t.lanes=e.lanes,gt(e,t,i)}return sl(e,t,n,r,i)}function Xd(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},B(Mn,Le),Le|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,B(Mn,Le),Le|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,B(Mn,Le),Le|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,B(Mn,Le),Le|=r;return xe(e,t,i,n),t.child}function Zd(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function sl(e,t,n,r,i){var o=Ee(n)?un:ye.current;return o=$n(t,o),Bn(t,i),n=fa(e,t,n,r,o,i),r=da(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gt(e,t,i)):(W&&r&&Jl(t),t.flags|=1,xe(e,t,n,i),t.child)}function Hu(e,t,n,r,i){if(Ee(n)){var o=!0;Yi(t)}else o=!1;if(Bn(t,i),t.stateNode===null)Di(e,t),Gd(t,n,r),il(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=He(u):(u=Ee(n)?un:ye.current,u=$n(t,u));var c=n.getDerivedStateFromProps,f=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Ou(t,s,r,u),Ct=!1;var d=t.memoizedState;s.state=d,eo(t,r,s,i),a=t.memoizedState,l!==r||d!==a||Te.current||Ct?(typeof c=="function"&&(rl(t,n,c,r),a=t.memoizedState),(l=Ct||Fu(t,n,l,r,d,a,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Td(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:Ke(t.type,l),s.props=u,f=t.pendingProps,d=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=He(a):(a=Ee(n)?un:ye.current,a=$n(t,a));var g=n.getDerivedStateFromProps;(c=typeof g=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==f||d!==a)&&Ou(t,s,r,a),Ct=!1,d=t.memoizedState,s.state=d,eo(t,r,s,i);var v=t.memoizedState;l!==f||d!==v||Te.current||Ct?(typeof g=="function"&&(rl(t,n,g,r),v=t.memoizedState),(u=Ct||Fu(t,n,u,r,d,v,a)||!1)?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,v,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,v,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),s.props=r,s.state=v,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return ll(e,t,n,r,o,i)}function ll(e,t,n,r,i,o){Zd(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&Vu(t,n,!1),gt(e,t,o);r=t.stateNode,W0.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Wn(t,e.child,null,o),t.child=Wn(t,null,l,o)):xe(e,t,l,o),t.memoizedState=r.state,i&&Vu(t,n,!0),t.child}function qd(e){var t=e.stateNode;t.pendingContext?Nu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Nu(e,t.context,!1),la(e,t.containerInfo)}function Wu(e,t,n,r,i){return Hn(),ta(i),t.flags|=256,xe(e,t,n,r),t.child}var al={dehydrated:null,treeContext:null,retryLane:0};function ul(e){return{baseLanes:e,cachePool:null,transitions:null}}function Jd(e,t,n){var r=t.pendingProps,i=G.current,o=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),B(G,i&1),e===null)return tl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Vo(s,r,0,null),e=an(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=ul(n),t.memoizedState=al,e):ma(t,s));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return G0(e,t,s,r,l,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Ft(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?o=Ft(l,o):(o=an(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?ul(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=al,r}return o=e.child,e=o.sibling,r=Ft(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ma(e,t){return t=Vo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function gi(e,t,n,r){return r!==null&&ta(r),Wn(t,e.child,null,n),e=ma(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function G0(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=cs(Error(k(422))),gi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Vo({mode:"visible",children:r.children},i,0,null),o=an(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&Wn(t,e.child,null,s),t.child.memoizedState=ul(s),t.memoizedState=al,o);if(!(t.mode&1))return gi(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,o=Error(k(419)),r=cs(o,r,void 0),gi(e,t,s,r)}if(l=(s&e.childLanes)!==0,Ce||l){if(r=le,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,yt(e,i),Ze(r,e,i,-1))}return Sa(),r=cs(Error(k(421))),gi(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=oy.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,Me=Dt(i.nextSibling),Ae=t,W=!0,Ye=null,e!==null&&(Be[be++]=at,Be[be++]=ut,Be[be++]=cn,at=e.id,ut=e.overflow,cn=t),t=ma(t,r.children),t.flags|=4096,t)}function Gu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),nl(e.return,t,n)}function fs(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function ep(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(xe(e,t,r.children,n),r=G.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Gu(e,n,t);else if(e.tag===19)Gu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(B(G,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&to(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),fs(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&to(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}fs(t,!0,n,null,o);break;case"together":fs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Di(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function gt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),dn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(k(153));if(t.child!==null){for(e=t.child,n=Ft(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ft(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function K0(e,t,n){switch(t.tag){case 3:qd(t),Hn();break;case 5:Ed(t);break;case 1:Ee(t.type)&&Yi(t);break;case 4:la(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;B(qi,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(B(G,G.current&1),t.flags|=128,null):n&t.child.childLanes?Jd(e,t,n):(B(G,G.current&1),e=gt(e,t,n),e!==null?e.sibling:null);B(G,G.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ep(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),B(G,G.current),r)break;return null;case 22:case 23:return t.lanes=0,Xd(e,t,n)}return gt(e,t,n)}var tp,cl,np,rp;tp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};cl=function(){};np=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,on(it.current);var o=null;switch(n){case"input":i=As(e,i),r=As(e,r),o=[];break;case"select":i=Y({},i,{value:void 0}),r=Y({},r,{value:void 0}),o=[];break;case"textarea":i=_s(e,i),r=_s(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ki)}Fs(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Nr.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(o||(o=[]),o.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(o=o||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(o=o||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Nr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&b("scroll",e),o||l===a||(o=[])):(o=o||[]).push(u,a))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};rp=function(e,t,n,r){n!==r&&(t.flags|=4)};function sr(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function pe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Q0(e,t,n){var r=t.pendingProps;switch(ea(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return pe(t),null;case 1:return Ee(t.type)&&Qi(),pe(t),null;case 3:return r=t.stateNode,Gn(),U(Te),U(ye),ua(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(mi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ye!==null&&(vl(Ye),Ye=null))),cl(e,t),pe(t),null;case 5:aa(t);var i=on(Br.current);if(n=t.type,e!==null&&t.stateNode!=null)np(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(k(166));return pe(t),null}if(e=on(it.current),mi(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[nt]=t,r[Or]=o,e=(t.mode&1)!==0,n){case"dialog":b("cancel",r),b("close",r);break;case"iframe":case"object":case"embed":b("load",r);break;case"video":case"audio":for(i=0;i<pr.length;i++)b(pr[i],r);break;case"source":b("error",r);break;case"img":case"image":case"link":b("error",r),b("load",r);break;case"details":b("toggle",r);break;case"input":tu(r,o),b("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},b("invalid",r);break;case"textarea":ru(r,o),b("invalid",r)}Fs(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="children"?typeof l=="string"?r.textContent!==l&&(o.suppressHydrationWarning!==!0&&hi(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(o.suppressHydrationWarning!==!0&&hi(r.textContent,l,e),i=["children",""+l]):Nr.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&b("scroll",r)}switch(n){case"input":si(r),nu(r,o,!0);break;case"textarea":si(r),iu(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Ki)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Mf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[nt]=t,e[Or]=r,tp(e,t,!1,!1),t.stateNode=e;e:{switch(s=Os(n,r),n){case"dialog":b("cancel",e),b("close",e),i=r;break;case"iframe":case"object":case"embed":b("load",e),i=r;break;case"video":case"audio":for(i=0;i<pr.length;i++)b(pr[i],e);i=r;break;case"source":b("error",e),i=r;break;case"img":case"image":case"link":b("error",e),b("load",e),i=r;break;case"details":b("toggle",e),i=r;break;case"input":tu(e,r),i=As(e,r),b("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=Y({},r,{value:void 0}),b("invalid",e);break;case"textarea":ru(e,r),i=_s(e,r),b("invalid",e);break;default:i=r}Fs(n,i),l=i;for(o in l)if(l.hasOwnProperty(o)){var a=l[o];o==="style"?Rf(e,a):o==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Af(e,a)):o==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Vr(e,a):typeof a=="number"&&Vr(e,""+a):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Nr.hasOwnProperty(o)?a!=null&&o==="onScroll"&&b("scroll",e):a!=null&&zl(e,o,a,s))}switch(n){case"input":si(e),nu(e,r,!1);break;case"textarea":si(e),iu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Bt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?In(e,!!r.multiple,o,!1):r.defaultValue!=null&&In(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Ki)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return pe(t),null;case 6:if(e&&t.stateNode!=null)rp(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(k(166));if(n=on(Br.current),on(it.current),mi(t)){if(r=t.stateNode,n=t.memoizedProps,r[nt]=t,(o=r.nodeValue!==n)&&(e=Ae,e!==null))switch(e.tag){case 3:hi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&hi(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[nt]=t,t.stateNode=r}return pe(t),null;case 13:if(U(G),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&Me!==null&&t.mode&1&&!(t.flags&128))Sd(),Hn(),t.flags|=98560,o=!1;else if(o=mi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(k(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(k(317));o[nt]=t}else Hn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;pe(t),o=!1}else Ye!==null&&(vl(Ye),Ye=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||G.current&1?re===0&&(re=3):Sa())),t.updateQueue!==null&&(t.flags|=4),pe(t),null);case 4:return Gn(),cl(e,t),e===null&&Ir(t.stateNode.containerInfo),pe(t),null;case 10:return ia(t.type._context),pe(t),null;case 17:return Ee(t.type)&&Qi(),pe(t),null;case 19:if(U(G),o=t.memoizedState,o===null)return pe(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)sr(o,!1);else{if(re!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=to(e),s!==null){for(t.flags|=128,sr(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return B(G,G.current&1|2),t.child}e=e.sibling}o.tail!==null&&J()>Qn&&(t.flags|=128,r=!0,sr(o,!1),t.lanes=4194304)}else{if(!r)if(e=to(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),sr(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!W)return pe(t),null}else 2*J()-o.renderingStartTime>Qn&&n!==1073741824&&(t.flags|=128,r=!0,sr(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=J(),t.sibling=null,n=G.current,B(G,r?n&1|2:n&1),t):(pe(t),null);case 22:case 23:return wa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Le&1073741824&&(pe(t),t.subtreeFlags&6&&(t.flags|=8192)):pe(t),null;case 24:return null;case 25:return null}throw Error(k(156,t.tag))}function Y0(e,t){switch(ea(t),t.tag){case 1:return Ee(t.type)&&Qi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Gn(),U(Te),U(ye),ua(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return aa(t),null;case 13:if(U(G),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(k(340));Hn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return U(G),null;case 4:return Gn(),null;case 10:return ia(t.type._context),null;case 22:case 23:return wa(),null;case 24:return null;default:return null}}var vi=!1,me=!1,X0=typeof WeakSet=="function"?WeakSet:Set,N=null;function Ln(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){X(e,t,r)}else n.current=null}function fl(e,t,n){try{n()}catch(r){X(e,t,r)}}var Ku=!1;function Z0(e,t){if(Qs=Hi,e=ad(),ql(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,c=0,f=e,d=null;t:for(;;){for(var g;f!==n||i!==0&&f.nodeType!==3||(l=s+i),f!==o||r!==0&&f.nodeType!==3||(a=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(g=f.firstChild)!==null;)d=f,f=g;for(;;){if(f===e)break t;if(d===n&&++u===i&&(l=s),d===o&&++c===r&&(a=s),(g=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=g}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ys={focusedElem:e,selectionRange:n},Hi=!1,N=t;N!==null;)if(t=N,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,N=e;else for(;N!==null;){t=N;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,T=v.memoizedState,m=t.stateNode,p=m.getSnapshotBeforeUpdate(t.elementType===t.type?x:Ke(t.type,x),T);m.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var h=t.stateNode.containerInfo;h.nodeType===1?h.textContent="":h.nodeType===9&&h.documentElement&&h.removeChild(h.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(k(163))}}catch(w){X(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,N=e;break}N=t.return}return v=Ku,Ku=!1,v}function Sr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&fl(t,n,o)}i=i.next}while(i!==r)}}function jo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function dl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function ip(e){var t=e.alternate;t!==null&&(e.alternate=null,ip(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[nt],delete t[Or],delete t[qs],delete t[D0],delete t[R0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function op(e){return e.tag===5||e.tag===3||e.tag===4}function Qu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||op(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function pl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ki));else if(r!==4&&(e=e.child,e!==null))for(pl(e,t,n),e=e.sibling;e!==null;)pl(e,t,n),e=e.sibling}function hl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(hl(e,t,n),e=e.sibling;e!==null;)hl(e,t,n),e=e.sibling}var ue=null,Qe=!1;function St(e,t,n){for(n=n.child;n!==null;)sp(e,t,n),n=n.sibling}function sp(e,t,n){if(rt&&typeof rt.onCommitFiberUnmount=="function")try{rt.onCommitFiberUnmount(xo,n)}catch{}switch(n.tag){case 5:me||Ln(n,t);case 6:var r=ue,i=Qe;ue=null,St(e,t,n),ue=r,Qe=i,ue!==null&&(Qe?(e=ue,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ue.removeChild(n.stateNode));break;case 18:ue!==null&&(Qe?(e=ue,n=n.stateNode,e.nodeType===8?is(e.parentNode,n):e.nodeType===1&&is(e,n),Dr(e)):is(ue,n.stateNode));break;case 4:r=ue,i=Qe,ue=n.stateNode.containerInfo,Qe=!0,St(e,t,n),ue=r,Qe=i;break;case 0:case 11:case 14:case 15:if(!me&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&fl(n,t,s),i=i.next}while(i!==r)}St(e,t,n);break;case 1:if(!me&&(Ln(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){X(n,t,l)}St(e,t,n);break;case 21:St(e,t,n);break;case 22:n.mode&1?(me=(r=me)||n.memoizedState!==null,St(e,t,n),me=r):St(e,t,n);break;default:St(e,t,n)}}function Yu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new X0),t.forEach(function(r){var i=sy.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Ge(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:ue=l.stateNode,Qe=!1;break e;case 3:ue=l.stateNode.containerInfo,Qe=!0;break e;case 4:ue=l.stateNode.containerInfo,Qe=!0;break e}l=l.return}if(ue===null)throw Error(k(160));sp(o,s,i),ue=null,Qe=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){X(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)lp(t,e),t=t.sibling}function lp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ge(t,e),et(e),r&4){try{Sr(3,e,e.return),jo(3,e)}catch(x){X(e,e.return,x)}try{Sr(5,e,e.return)}catch(x){X(e,e.return,x)}}break;case 1:Ge(t,e),et(e),r&512&&n!==null&&Ln(n,n.return);break;case 5:if(Ge(t,e),et(e),r&512&&n!==null&&Ln(n,n.return),e.flags&32){var i=e.stateNode;try{Vr(i,"")}catch(x){X(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&o.type==="radio"&&o.name!=null&&Vf(i,o),Os(l,s);var u=Os(l,o);for(s=0;s<a.length;s+=2){var c=a[s],f=a[s+1];c==="style"?Rf(i,f):c==="dangerouslySetInnerHTML"?Af(i,f):c==="children"?Vr(i,f):zl(i,c,f,u)}switch(l){case"input":Ds(i,o);break;case"textarea":Lf(i,o);break;case"select":var d=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var g=o.value;g!=null?In(i,!!o.multiple,g,!1):d!==!!o.multiple&&(o.defaultValue!=null?In(i,!!o.multiple,o.defaultValue,!0):In(i,!!o.multiple,o.multiple?[]:"",!1))}i[Or]=o}catch(x){X(e,e.return,x)}}break;case 6:if(Ge(t,e),et(e),r&4){if(e.stateNode===null)throw Error(k(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(x){X(e,e.return,x)}}break;case 3:if(Ge(t,e),et(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Dr(t.containerInfo)}catch(x){X(e,e.return,x)}break;case 4:Ge(t,e),et(e);break;case 13:Ge(t,e),et(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(va=J())),r&4&&Yu(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(me=(u=me)||c,Ge(t,e),me=u):Ge(t,e),et(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(N=e,c=e.child;c!==null;){for(f=N=c;N!==null;){switch(d=N,g=d.child,d.tag){case 0:case 11:case 14:case 15:Sr(4,d,d.return);break;case 1:Ln(d,d.return);var v=d.stateNode;if(typeof v.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){X(r,n,x)}}break;case 5:Ln(d,d.return);break;case 22:if(d.memoizedState!==null){Zu(f);continue}}g!==null?(g.return=d,N=g):Zu(f)}c=c.sibling}e:for(c=null,f=e;;){if(f.tag===5){if(c===null){c=f;try{i=f.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(l=f.stateNode,a=f.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Df("display",s))}catch(x){X(e,e.return,x)}}}else if(f.tag===6){if(c===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(x){X(e,e.return,x)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;c===f&&(c=null),f=f.return}c===f&&(c=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Ge(t,e),et(e),r&4&&Yu(e);break;case 21:break;default:Ge(t,e),et(e)}}function et(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(op(n)){var r=n;break e}n=n.return}throw Error(k(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Vr(i,""),r.flags&=-33);var o=Qu(e);hl(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,l=Qu(e);pl(e,l,s);break;default:throw Error(k(161))}}catch(a){X(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function q0(e,t,n){N=e,ap(e)}function ap(e,t,n){for(var r=(e.mode&1)!==0;N!==null;){var i=N,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||vi;if(!s){var l=i.alternate,a=l!==null&&l.memoizedState!==null||me;l=vi;var u=me;if(vi=s,(me=a)&&!u)for(N=i;N!==null;)s=N,a=s.child,s.tag===22&&s.memoizedState!==null?qu(i):a!==null?(a.return=s,N=a):qu(i);for(;o!==null;)N=o,ap(o),o=o.sibling;N=i,vi=l,me=u}Xu(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,N=o):Xu(e)}}function Xu(e){for(;N!==null;){var t=N;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:me||jo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!me)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ke(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Ru(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Ru(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var f=c.dehydrated;f!==null&&Dr(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(k(163))}me||t.flags&512&&dl(t)}catch(d){X(t,t.return,d)}}if(t===e){N=null;break}if(n=t.sibling,n!==null){n.return=t.return,N=n;break}N=t.return}}function Zu(e){for(;N!==null;){var t=N;if(t===e){N=null;break}var n=t.sibling;if(n!==null){n.return=t.return,N=n;break}N=t.return}}function qu(e){for(;N!==null;){var t=N;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{jo(4,t)}catch(a){X(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){X(t,i,a)}}var o=t.return;try{dl(t)}catch(a){X(t,o,a)}break;case 5:var s=t.return;try{dl(t)}catch(a){X(t,s,a)}}}catch(a){X(t,t.return,a)}if(t===e){N=null;break}var l=t.sibling;if(l!==null){l.return=t.return,N=l;break}N=t.return}}var J0=Math.ceil,io=xt.ReactCurrentDispatcher,ya=xt.ReactCurrentOwner,$e=xt.ReactCurrentBatchConfig,O=0,le=null,te=null,ce=0,Le=0,Mn=Wt(0),re=0,Hr=null,dn=0,No=0,ga=0,kr=null,Pe=null,va=0,Qn=1/0,st=null,oo=!1,ml=null,_t=null,xi=!1,Vt=null,so=0,Pr=0,yl=null,Ri=-1,_i=0;function we(){return O&6?J():Ri!==-1?Ri:Ri=J()}function It(e){return e.mode&1?O&2&&ce!==0?ce&-ce:I0.transition!==null?(_i===0&&(_i=Gf()),_i):(e=z,e!==0||(e=window.event,e=e===void 0?16:Jf(e.type)),e):1}function Ze(e,t,n,r){if(50<Pr)throw Pr=0,yl=null,Error(k(185));Yr(e,n,r),(!(O&2)||e!==le)&&(e===le&&(!(O&2)&&(No|=n),re===4&&jt(e,ce)),je(e,r),n===1&&O===0&&!(t.mode&1)&&(Qn=J()+500,Co&&Gt()))}function je(e,t){var n=e.callbackNode;Im(e,t);var r=$i(e,e===le?ce:0);if(r===0)n!==null&&lu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&lu(n),t===1)e.tag===0?_0(Ju.bind(null,e)):vd(Ju.bind(null,e)),M0(function(){!(O&6)&&Gt()}),n=null;else{switch(Kf(r)){case 1:n=Hl;break;case 4:n=Hf;break;case 16:n=Ui;break;case 536870912:n=Wf;break;default:n=Ui}n=yp(n,up.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function up(e,t){if(Ri=-1,_i=0,O&6)throw Error(k(327));var n=e.callbackNode;if(bn()&&e.callbackNode!==n)return null;var r=$i(e,e===le?ce:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=lo(e,r);else{t=r;var i=O;O|=2;var o=fp();(le!==e||ce!==t)&&(st=null,Qn=J()+500,ln(e,t));do try{ny();break}catch(l){cp(e,l)}while(1);ra(),io.current=o,O=i,te!==null?t=0:(le=null,ce=0,t=re)}if(t!==0){if(t===2&&(i=$s(e),i!==0&&(r=i,t=gl(e,i))),t===1)throw n=Hr,ln(e,0),jt(e,r),je(e,J()),n;if(t===6)jt(e,r);else{if(i=e.current.alternate,!(r&30)&&!ey(i)&&(t=lo(e,r),t===2&&(o=$s(e),o!==0&&(r=o,t=gl(e,o))),t===1))throw n=Hr,ln(e,0),jt(e,r),je(e,J()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(k(345));case 2:Jt(e,Pe,st);break;case 3:if(jt(e,r),(r&130023424)===r&&(t=va+500-J(),10<t)){if($i(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){we(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Zs(Jt.bind(null,e,Pe,st),t);break}Jt(e,Pe,st);break;case 4:if(jt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Xe(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=J()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*J0(r/1960))-r,10<r){e.timeoutHandle=Zs(Jt.bind(null,e,Pe,st),r);break}Jt(e,Pe,st);break;case 5:Jt(e,Pe,st);break;default:throw Error(k(329))}}}return je(e,J()),e.callbackNode===n?up.bind(null,e):null}function gl(e,t){var n=kr;return e.current.memoizedState.isDehydrated&&(ln(e,t).flags|=256),e=lo(e,t),e!==2&&(t=Pe,Pe=n,t!==null&&vl(t)),e}function vl(e){Pe===null?Pe=e:Pe.push.apply(Pe,e)}function ey(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!qe(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function jt(e,t){for(t&=~ga,t&=~No,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Xe(t),r=1<<n;e[n]=-1,t&=~r}}function Ju(e){if(O&6)throw Error(k(327));bn();var t=$i(e,0);if(!(t&1))return je(e,J()),null;var n=lo(e,t);if(e.tag!==0&&n===2){var r=$s(e);r!==0&&(t=r,n=gl(e,r))}if(n===1)throw n=Hr,ln(e,0),jt(e,t),je(e,J()),n;if(n===6)throw Error(k(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Jt(e,Pe,st),je(e,J()),null}function xa(e,t){var n=O;O|=1;try{return e(t)}finally{O=n,O===0&&(Qn=J()+500,Co&&Gt())}}function pn(e){Vt!==null&&Vt.tag===0&&!(O&6)&&bn();var t=O;O|=1;var n=$e.transition,r=z;try{if($e.transition=null,z=1,e)return e()}finally{z=r,$e.transition=n,O=t,!(O&6)&&Gt()}}function wa(){Le=Mn.current,U(Mn)}function ln(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,L0(n)),te!==null)for(n=te.return;n!==null;){var r=n;switch(ea(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Qi();break;case 3:Gn(),U(Te),U(ye),ua();break;case 5:aa(r);break;case 4:Gn();break;case 13:U(G);break;case 19:U(G);break;case 10:ia(r.type._context);break;case 22:case 23:wa()}n=n.return}if(le=e,te=e=Ft(e.current,null),ce=Le=t,re=0,Hr=null,ga=No=dn=0,Pe=kr=null,rn!==null){for(t=0;t<rn.length;t++)if(n=rn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}rn=null}return e}function cp(e,t){do{var n=te;try{if(ra(),Mi.current=ro,no){for(var r=Q.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}no=!1}if(fn=0,se=ne=Q=null,wr=!1,br=0,ya.current=null,n===null||n.return===null){re=1,Hr=t,te=null;break}e:{var o=e,s=n.return,l=n,a=t;if(t=ce,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,c=l,f=c.tag;if(!(c.mode&1)&&(f===0||f===11||f===15)){var d=c.alternate;d?(c.updateQueue=d.updateQueue,c.memoizedState=d.memoizedState,c.lanes=d.lanes):(c.updateQueue=null,c.memoizedState=null)}var g=Bu(s);if(g!==null){g.flags&=-257,bu(g,s,l,o,t),g.mode&1&&zu(o,u,t),t=g,a=u;var v=t.updateQueue;if(v===null){var x=new Set;x.add(a),t.updateQueue=x}else v.add(a);break e}else{if(!(t&1)){zu(o,u,t),Sa();break e}a=Error(k(426))}}else if(W&&l.mode&1){var T=Bu(s);if(T!==null){!(T.flags&65536)&&(T.flags|=256),bu(T,s,l,o,t),ta(Kn(a,l));break e}}o=a=Kn(a,l),re!==4&&(re=2),kr===null?kr=[o]:kr.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var m=Kd(o,a,t);Du(o,m);break e;case 1:l=a;var p=o.type,h=o.stateNode;if(!(o.flags&128)&&(typeof p.getDerivedStateFromError=="function"||h!==null&&typeof h.componentDidCatch=="function"&&(_t===null||!_t.has(h)))){o.flags|=65536,t&=-t,o.lanes|=t;var w=Qd(o,l,t);Du(o,w);break e}}o=o.return}while(o!==null)}pp(n)}catch(S){t=S,te===n&&n!==null&&(te=n=n.return);continue}break}while(1)}function fp(){var e=io.current;return io.current=ro,e===null?ro:e}function Sa(){(re===0||re===3||re===2)&&(re=4),le===null||!(dn&268435455)&&!(No&268435455)||jt(le,ce)}function lo(e,t){var n=O;O|=2;var r=fp();(le!==e||ce!==t)&&(st=null,ln(e,t));do try{ty();break}catch(i){cp(e,i)}while(1);if(ra(),O=n,io.current=r,te!==null)throw Error(k(261));return le=null,ce=0,re}function ty(){for(;te!==null;)dp(te)}function ny(){for(;te!==null&&!jm();)dp(te)}function dp(e){var t=mp(e.alternate,e,Le);e.memoizedProps=e.pendingProps,t===null?pp(e):te=t,ya.current=null}function pp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Y0(n,t),n!==null){n.flags&=32767,te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{re=6,te=null;return}}else if(n=Q0(n,t,Le),n!==null){te=n;return}if(t=t.sibling,t!==null){te=t;return}te=t=e}while(t!==null);re===0&&(re=5)}function Jt(e,t,n){var r=z,i=$e.transition;try{$e.transition=null,z=1,ry(e,t,n,r)}finally{$e.transition=i,z=r}return null}function ry(e,t,n,r){do bn();while(Vt!==null);if(O&6)throw Error(k(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(k(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Fm(e,o),e===le&&(te=le=null,ce=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||xi||(xi=!0,yp(Ui,function(){return bn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=$e.transition,$e.transition=null;var s=z;z=1;var l=O;O|=4,ya.current=null,Z0(e,n),lp(n,e),P0(Ys),Hi=!!Qs,Ys=Qs=null,e.current=n,q0(n),Nm(),O=l,z=s,$e.transition=o}else e.current=n;if(xi&&(xi=!1,Vt=e,so=i),o=e.pendingLanes,o===0&&(_t=null),Mm(n.stateNode),je(e,J()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(oo)throw oo=!1,e=ml,ml=null,e;return so&1&&e.tag!==0&&bn(),o=e.pendingLanes,o&1?e===yl?Pr++:(Pr=0,yl=e):Pr=0,Gt(),null}function bn(){if(Vt!==null){var e=Kf(so),t=$e.transition,n=z;try{if($e.transition=null,z=16>e?16:e,Vt===null)var r=!1;else{if(e=Vt,Vt=null,so=0,O&6)throw Error(k(331));var i=O;for(O|=4,N=e.current;N!==null;){var o=N,s=o.child;if(N.flags&16){var l=o.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(N=u;N!==null;){var c=N;switch(c.tag){case 0:case 11:case 15:Sr(8,c,o)}var f=c.child;if(f!==null)f.return=c,N=f;else for(;N!==null;){c=N;var d=c.sibling,g=c.return;if(ip(c),c===u){N=null;break}if(d!==null){d.return=g,N=d;break}N=g}}}var v=o.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var T=x.sibling;x.sibling=null,x=T}while(x!==null)}}N=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,N=s;else e:for(;N!==null;){if(o=N,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Sr(9,o,o.return)}var m=o.sibling;if(m!==null){m.return=o.return,N=m;break e}N=o.return}}var p=e.current;for(N=p;N!==null;){s=N;var h=s.child;if(s.subtreeFlags&2064&&h!==null)h.return=s,N=h;else e:for(s=p;N!==null;){if(l=N,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:jo(9,l)}}catch(S){X(l,l.return,S)}if(l===s){N=null;break e}var w=l.sibling;if(w!==null){w.return=l.return,N=w;break e}N=l.return}}if(O=i,Gt(),rt&&typeof rt.onPostCommitFiberRoot=="function")try{rt.onPostCommitFiberRoot(xo,e)}catch{}r=!0}return r}finally{z=n,$e.transition=t}}return!1}function ec(e,t,n){t=Kn(n,t),t=Kd(e,t,1),e=Rt(e,t,1),t=we(),e!==null&&(Yr(e,1,t),je(e,t))}function X(e,t,n){if(e.tag===3)ec(e,e,n);else for(;t!==null;){if(t.tag===3){ec(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(_t===null||!_t.has(r))){e=Kn(n,e),e=Qd(t,e,1),t=Rt(t,e,1),e=we(),t!==null&&(Yr(t,1,e),je(t,e));break}}t=t.return}}function iy(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=we(),e.pingedLanes|=e.suspendedLanes&n,le===e&&(ce&n)===n&&(re===4||re===3&&(ce&130023424)===ce&&500>J()-va?ln(e,0):ga|=n),je(e,t)}function hp(e,t){t===0&&(e.mode&1?(t=ui,ui<<=1,!(ui&130023424)&&(ui=4194304)):t=1);var n=we();e=yt(e,t),e!==null&&(Yr(e,t,n),je(e,n))}function oy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),hp(e,n)}function sy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(k(314))}r!==null&&r.delete(t),hp(e,n)}var mp;mp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Te.current)Ce=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ce=!1,K0(e,t,n);Ce=!!(e.flags&131072)}else Ce=!1,W&&t.flags&1048576&&xd(t,Zi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Di(e,t),e=t.pendingProps;var i=$n(t,ye.current);Bn(t,n),i=fa(null,t,r,e,i,n);var o=da();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ee(r)?(o=!0,Yi(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,sa(t),i.updater=Eo,t.stateNode=i,i._reactInternals=t,il(t,r,e,n),t=ll(null,t,r,!0,o,n)):(t.tag=0,W&&o&&Jl(t),xe(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Di(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=ay(r),e=Ke(r,e),i){case 0:t=sl(null,t,r,e,n);break e;case 1:t=Hu(null,t,r,e,n);break e;case 11:t=Uu(null,t,r,e,n);break e;case 14:t=$u(null,t,r,Ke(r.type,e),n);break e}throw Error(k(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),sl(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),Hu(e,t,r,i,n);case 3:e:{if(qd(t),e===null)throw Error(k(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Td(e,t),eo(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=Kn(Error(k(423)),t),t=Wu(e,t,r,n,i);break e}else if(r!==i){i=Kn(Error(k(424)),t),t=Wu(e,t,r,n,i);break e}else for(Me=Dt(t.stateNode.containerInfo.firstChild),Ae=t,W=!0,Ye=null,n=Pd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Hn(),r===i){t=gt(e,t,n);break e}xe(e,t,r,n)}t=t.child}return t;case 5:return Ed(t),e===null&&tl(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,Xs(r,i)?s=null:o!==null&&Xs(r,o)&&(t.flags|=32),Zd(e,t),xe(e,t,s,n),t.child;case 6:return e===null&&tl(t),null;case 13:return Jd(e,t,n);case 4:return la(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Wn(t,null,r,n):xe(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),Uu(e,t,r,i,n);case 7:return xe(e,t,t.pendingProps,n),t.child;case 8:return xe(e,t,t.pendingProps.children,n),t.child;case 12:return xe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,B(qi,r._currentValue),r._currentValue=s,o!==null)if(qe(o.value,s)){if(o.children===i.children&&!Te.current){t=gt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var l=o.dependencies;if(l!==null){s=o.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(o.tag===1){a=ft(-1,n&-n),a.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?a.next=a:(a.next=c.next,c.next=a),u.pending=a}}o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),nl(o.return,n,t),l.lanes|=n;break}a=a.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(k(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),nl(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}xe(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Bn(t,n),i=He(i),r=r(i),t.flags|=1,xe(e,t,r,n),t.child;case 14:return r=t.type,i=Ke(r,t.pendingProps),i=Ke(r.type,i),$u(e,t,r,i,n);case 15:return Yd(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),Di(e,t),t.tag=1,Ee(r)?(e=!0,Yi(t)):e=!1,Bn(t,n),Gd(t,r,i),il(t,r,i,n),ll(null,t,r,!0,e,n);case 19:return ep(e,t,n);case 22:return Xd(e,t,n)}throw Error(k(156,t.tag))};function yp(e,t){return $f(e,t)}function ly(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ue(e,t,n,r){return new ly(e,t,n,r)}function ka(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ay(e){if(typeof e=="function")return ka(e)?1:0;if(e!=null){if(e=e.$$typeof,e===bl)return 11;if(e===Ul)return 14}return 2}function Ft(e,t){var n=e.alternate;return n===null?(n=Ue(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ii(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")ka(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Sn:return an(n.children,i,o,t);case Bl:s=8,i|=8;break;case Ns:return e=Ue(12,n,t,i|2),e.elementType=Ns,e.lanes=o,e;case Vs:return e=Ue(13,n,t,i),e.elementType=Vs,e.lanes=o,e;case Ls:return e=Ue(19,n,t,i),e.elementType=Ls,e.lanes=o,e;case Ef:return Vo(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Cf:s=10;break e;case Tf:s=9;break e;case bl:s=11;break e;case Ul:s=14;break e;case Pt:s=16,r=null;break e}throw Error(k(130,e==null?e:typeof e,""))}return t=Ue(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function an(e,t,n,r){return e=Ue(7,e,r,t),e.lanes=n,e}function Vo(e,t,n,r){return e=Ue(22,e,r,t),e.elementType=Ef,e.lanes=n,e.stateNode={isHidden:!1},e}function ds(e,t,n){return e=Ue(6,e,null,t),e.lanes=n,e}function ps(e,t,n){return t=Ue(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function uy(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ko(0),this.expirationTimes=Ko(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ko(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Pa(e,t,n,r,i,o,s,l,a){return e=new uy(e,t,n,l,a),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Ue(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},sa(o),e}function cy(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:wn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function gp(e){if(!e)return bt;e=e._reactInternals;e:{if(mn(e)!==e||e.tag!==1)throw Error(k(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ee(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(k(171))}if(e.tag===1){var n=e.type;if(Ee(n))return gd(e,n,t)}return t}function vp(e,t,n,r,i,o,s,l,a){return e=Pa(n,r,!0,e,i,o,s,l,a),e.context=gp(null),n=e.current,r=we(),i=It(n),o=ft(r,i),o.callback=t??null,Rt(n,o,i),e.current.lanes=i,Yr(e,i,r),je(e,r),e}function Lo(e,t,n,r){var i=t.current,o=we(),s=It(i);return n=gp(n),t.context===null?t.context=n:t.pendingContext=n,t=ft(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Rt(i,t,s),e!==null&&(Ze(e,i,s,o),Li(e,i,s)),s}function ao(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function tc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Ca(e,t){tc(e,t),(e=e.alternate)&&tc(e,t)}function fy(){return null}var xp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ta(e){this._internalRoot=e}Mo.prototype.render=Ta.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(k(409));Lo(e,t,null,null)};Mo.prototype.unmount=Ta.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;pn(function(){Lo(null,e,null,null)}),t[mt]=null}};function Mo(e){this._internalRoot=e}Mo.prototype.unstable_scheduleHydration=function(e){if(e){var t=Xf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Et.length&&t!==0&&t<Et[n].priority;n++);Et.splice(n,0,e),n===0&&qf(e)}};function Ea(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ao(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function nc(){}function dy(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=ao(s);o.call(u)}}var s=vp(t,r,e,0,null,!1,!1,"",nc);return e._reactRootContainer=s,e[mt]=s.current,Ir(e.nodeType===8?e.parentNode:e),pn(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=ao(a);l.call(u)}}var a=Pa(e,0,!1,null,null,!1,!1,"",nc);return e._reactRootContainer=a,e[mt]=a.current,Ir(e.nodeType===8?e.parentNode:e),pn(function(){Lo(t,a,n,r)}),a}function Do(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var l=i;i=function(){var a=ao(s);l.call(a)}}Lo(t,s,e,i)}else s=dy(n,t,e,i,r);return ao(s)}Qf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dr(t.pendingLanes);n!==0&&(Wl(t,n|1),je(t,J()),!(O&6)&&(Qn=J()+500,Gt()))}break;case 13:pn(function(){var r=yt(e,1);if(r!==null){var i=we();Ze(r,e,1,i)}}),Ca(e,1)}};Gl=function(e){if(e.tag===13){var t=yt(e,134217728);if(t!==null){var n=we();Ze(t,e,134217728,n)}Ca(e,134217728)}};Yf=function(e){if(e.tag===13){var t=It(e),n=yt(e,t);if(n!==null){var r=we();Ze(n,e,t,r)}Ca(e,t)}};Xf=function(){return z};Zf=function(e,t){var n=z;try{return z=e,t()}finally{z=n}};Bs=function(e,t,n){switch(t){case"input":if(Ds(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Po(r);if(!i)throw Error(k(90));Nf(r),Ds(r,i)}}}break;case"textarea":Lf(e,n);break;case"select":t=n.value,t!=null&&In(e,!!n.multiple,t,!1)}};Ff=xa;Of=pn;var py={usingClientEntryPoint:!1,Events:[Zr,Tn,Po,_f,If,xa]},lr={findFiberByHostInstance:nn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},hy={bundleType:lr.bundleType,version:lr.version,rendererPackageName:lr.rendererPackageName,rendererConfig:lr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:xt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=bf(e),e===null?null:e.stateNode},findFiberByHostInstance:lr.findFiberByHostInstance||fy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var wi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!wi.isDisabled&&wi.supportsFiber)try{xo=wi.inject(hy),rt=wi}catch{}}_e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=py;_e.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ea(t))throw Error(k(200));return cy(e,t,null,n)};_e.createRoot=function(e,t){if(!Ea(e))throw Error(k(299));var n=!1,r="",i=xp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Pa(e,1,!1,null,null,n,!1,r,i),e[mt]=t.current,Ir(e.nodeType===8?e.parentNode:e),new Ta(t)};_e.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(k(188)):(e=Object.keys(e).join(","),Error(k(268,e)));return e=bf(t),e=e===null?null:e.stateNode,e};_e.flushSync=function(e){return pn(e)};_e.hydrate=function(e,t,n){if(!Ao(t))throw Error(k(200));return Do(null,e,t,!0,n)};_e.hydrateRoot=function(e,t,n){if(!Ea(e))throw Error(k(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=xp;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=vp(t,null,e,1,n??null,i,!1,o,s),e[mt]=t.current,Ir(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Mo(t)};_e.render=function(e,t,n){if(!Ao(t))throw Error(k(200));return Do(null,e,t,!1,n)};_e.unmountComponentAtNode=function(e){if(!Ao(e))throw Error(k(40));return e._reactRootContainer?(pn(function(){Do(null,null,e,!1,function(){e._reactRootContainer=null,e[mt]=null})}),!0):!1};_e.unstable_batchedUpdates=xa;_e.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ao(n))throw Error(k(200));if(e==null||e._reactInternals===void 0)throw Error(k(38));return Do(e,t,n,!1,r)};_e.version="18.3.1-next-f1338f8080-20240426";function wp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(wp)}catch(e){console.error(e)}}wp(),wf.exports=_e;var my=wf.exports,rc=my;Es.createRoot=rc.createRoot,Es.hydrateRoot=rc.hydrateRoot;const Sp=R.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Ro=R.createContext({}),ja=R.createContext(null),_o=typeof document<"u",yy=_o?R.useLayoutEffect:R.useEffect,kp=R.createContext({strict:!1}),Na=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),gy="framerAppearId",Pp="data-"+Na(gy);function vy(e,t,n,r){const{visualElement:i}=R.useContext(Ro),o=R.useContext(kp),s=R.useContext(ja),l=R.useContext(Sp).reducedMotion,a=R.useRef();r=r||o.renderer,!a.current&&r&&(a.current=r(e,{visualState:t,parent:i,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:l}));const u=a.current;R.useInsertionEffect(()=>{u&&u.update(n,s)});const c=R.useRef(!!(n[Pp]&&!window.HandoffComplete));return yy(()=>{u&&(u.render(),c.current&&u.animationState&&u.animationState.animateChanges())}),R.useEffect(()=>{u&&(u.updateFeatures(),!c.current&&u.animationState&&u.animationState.animateChanges(),c.current&&(c.current=!1,window.HandoffComplete=!0))}),u}function An(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function xy(e,t,n){return R.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):An(n)&&(n.current=r))},[t])}function Wr(e){return typeof e=="string"||Array.isArray(e)}function Io(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Va=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],La=["initial",...Va];function Fo(e){return Io(e.animate)||La.some(t=>Wr(e[t]))}function Cp(e){return!!(Fo(e)||e.variants)}function wy(e,t){if(Fo(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Wr(n)?n:void 0,animate:Wr(r)?r:void 0}}return e.inherit!==!1?t:{}}function Sy(e){const{initial:t,animate:n}=wy(e,R.useContext(Ro));return R.useMemo(()=>({initial:t,animate:n}),[ic(t),ic(n)])}function ic(e){return Array.isArray(e)?e.join(" "):e}const oc={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Gr={};for(const e in oc)Gr[e]={isEnabled:t=>oc[e].some(n=>!!t[n])};function ky(e){for(const t in e)Gr[t]={...Gr[t],...e[t]}}const Tp=R.createContext({}),Ep=R.createContext({}),Py=Symbol.for("motionComponentSymbol");function Cy({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&ky(e);function o(l,a){let u;const c={...R.useContext(Sp),...l,layoutId:Ty(l)},{isStatic:f}=c,d=Sy(l),g=r(l,f);if(!f&&_o){d.visualElement=vy(i,g,c,t);const v=R.useContext(Ep),x=R.useContext(kp).strict;d.visualElement&&(u=d.visualElement.loadFeatures(c,x,e,v))}return R.createElement(Ro.Provider,{value:d},u&&d.visualElement?R.createElement(u,{visualElement:d.visualElement,...c}):null,n(i,l,xy(g,d.visualElement,a),g,f,d.visualElement))}const s=R.forwardRef(o);return s[Py]=i,s}function Ty({layoutId:e}){const t=R.useContext(Tp).id;return t&&e!==void 0?t+"-"+e:e}function Ey(e){function t(r,i={}){return Cy(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const jy=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Ma(e){return typeof e!="string"||e.includes("-")?!1:!!(jy.indexOf(e)>-1||/[A-Z]/.test(e))}const uo={};function Ny(e){Object.assign(uo,e)}const Jr=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],yn=new Set(Jr);function jp(e,{layout:t,layoutId:n}){return yn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!uo[e]||e==="opacity")}const Ne=e=>!!(e&&e.getVelocity),Vy={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Ly=Jr.length;function My(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let o="";for(let s=0;s<Ly;s++){const l=Jr[s];if(e[l]!==void 0){const a=Vy[l]||l;o+=`${a}(${e[l]}) `}}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,r?"":o):n&&r&&(o="none"),o}const Np=e=>t=>typeof t=="string"&&t.startsWith(e),Vp=Np("--"),xl=Np("var(--"),Ay=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,Dy=(e,t)=>t&&typeof e=="number"?t.transform(e):e,Ut=(e,t,n)=>Math.min(Math.max(n,e),t),gn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Cr={...gn,transform:e=>Ut(0,1,e)},Si={...gn,default:1},Tr=e=>Math.round(e*1e5)/1e5,Oo=/(-)?([\d]*\.?[\d])+/g,Lp=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Ry=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function ei(e){return typeof e=="string"}const ti=e=>({test:t=>ei(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),kt=ti("deg"),ot=ti("%"),L=ti("px"),_y=ti("vh"),Iy=ti("vw"),sc={...ot,parse:e=>ot.parse(e)/100,transform:e=>ot.transform(e*100)},lc={...gn,transform:Math.round},Mp={borderWidth:L,borderTopWidth:L,borderRightWidth:L,borderBottomWidth:L,borderLeftWidth:L,borderRadius:L,radius:L,borderTopLeftRadius:L,borderTopRightRadius:L,borderBottomRightRadius:L,borderBottomLeftRadius:L,width:L,maxWidth:L,height:L,maxHeight:L,size:L,top:L,right:L,bottom:L,left:L,padding:L,paddingTop:L,paddingRight:L,paddingBottom:L,paddingLeft:L,margin:L,marginTop:L,marginRight:L,marginBottom:L,marginLeft:L,rotate:kt,rotateX:kt,rotateY:kt,rotateZ:kt,scale:Si,scaleX:Si,scaleY:Si,scaleZ:Si,skew:kt,skewX:kt,skewY:kt,distance:L,translateX:L,translateY:L,translateZ:L,x:L,y:L,z:L,perspective:L,transformPerspective:L,opacity:Cr,originX:sc,originY:sc,originZ:L,zIndex:lc,fillOpacity:Cr,strokeOpacity:Cr,numOctaves:lc};function Aa(e,t,n,r){const{style:i,vars:o,transform:s,transformOrigin:l}=e;let a=!1,u=!1,c=!0;for(const f in t){const d=t[f];if(Vp(f)){o[f]=d;continue}const g=Mp[f],v=Dy(d,g);if(yn.has(f)){if(a=!0,s[f]=v,!c)continue;d!==(g.default||0)&&(c=!1)}else f.startsWith("origin")?(u=!0,l[f]=v):i[f]=v}if(t.transform||(a||r?i.transform=My(e.transform,n,c,r):i.transform&&(i.transform="none")),u){const{originX:f="50%",originY:d="50%",originZ:g=0}=l;i.transformOrigin=`${f} ${d} ${g}`}}const Da=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Ap(e,t,n){for(const r in t)!Ne(t[r])&&!jp(r,n)&&(e[r]=t[r])}function Fy({transformTemplate:e},t,n){return R.useMemo(()=>{const r=Da();return Aa(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function Oy(e,t,n){const r=e.style||{},i={};return Ap(i,r,e),Object.assign(i,Fy(e,t,n)),e.transformValues?e.transformValues(i):i}function zy(e,t,n){const r={},i=Oy(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const By=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function co(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||By.has(e)}let Dp=e=>!co(e);function by(e){e&&(Dp=t=>t.startsWith("on")?!co(t):e(t))}try{by(require("@emotion/is-prop-valid").default)}catch{}function Uy(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(Dp(i)||n===!0&&co(i)||!t&&!co(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function ac(e,t,n){return typeof e=="string"?e:L.transform(t+n*e)}function $y(e,t,n){const r=ac(t,e.x,e.width),i=ac(n,e.y,e.height);return`${r} ${i}`}const Hy={offset:"stroke-dashoffset",array:"stroke-dasharray"},Wy={offset:"strokeDashoffset",array:"strokeDasharray"};function Gy(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?Hy:Wy;e[o.offset]=L.transform(-r);const s=L.transform(t),l=L.transform(n);e[o.array]=`${s} ${l}`}function Ra(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:l=1,pathOffset:a=0,...u},c,f,d){if(Aa(e,u,c,d),f){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:g,style:v,dimensions:x}=e;g.transform&&(x&&(v.transform=g.transform),delete g.transform),x&&(i!==void 0||o!==void 0||v.transform)&&(v.transformOrigin=$y(x,i!==void 0?i:.5,o!==void 0?o:.5)),t!==void 0&&(g.x=t),n!==void 0&&(g.y=n),r!==void 0&&(g.scale=r),s!==void 0&&Gy(g,s,l,a,!1)}const Rp=()=>({...Da(),attrs:{}}),_a=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Ky(e,t,n,r){const i=R.useMemo(()=>{const o=Rp();return Ra(o,t,{enableHardwareAcceleration:!1},_a(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};Ap(o,e.style,e),i.style={...o,...i.style}}return i}function Qy(e=!1){return(n,r,i,{latestValues:o},s)=>{const a=(Ma(n)?Ky:zy)(r,o,s,n),c={...Uy(r,typeof n=="string",e),...a,ref:i},{children:f}=r,d=R.useMemo(()=>Ne(f)?f.get():f,[f]);return R.createElement(n,{...c,children:d})}}function _p(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const Ip=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Fp(e,t,n,r){_p(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(Ip.has(i)?i:Na(i),t.attrs[i])}function Ia(e,t){const{style:n}=e,r={};for(const i in n)(Ne(n[i])||t.style&&Ne(t.style[i])||jp(i,e))&&(r[i]=n[i]);return r}function Op(e,t){const n=Ia(e,t);for(const r in e)if(Ne(e[r])||Ne(t[r])){const i=Jr.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=e[r]}return n}function Fa(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}function Yy(e){const t=R.useRef(null);return t.current===null&&(t.current=e()),t.current}const fo=e=>Array.isArray(e),Xy=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Zy=e=>fo(e)?e[e.length-1]||0:e;function Fi(e){const t=Ne(e)?e.get():e;return Xy(t)?t.toValue():t}function qy({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,o){const s={latestValues:Jy(r,i,o,e),renderState:t()};return n&&(s.mount=l=>n(r,l,s)),s}const zp=e=>(t,n)=>{const r=R.useContext(Ro),i=R.useContext(ja),o=()=>qy(e,t,r,i);return n?o():Yy(o)};function Jy(e,t,n,r){const i={},o=r(e,{});for(const d in o)i[d]=Fi(o[d]);let{initial:s,animate:l}=e;const a=Fo(e),u=Cp(e);t&&u&&!a&&e.inherit!==!1&&(s===void 0&&(s=t.initial),l===void 0&&(l=t.animate));let c=n?n.initial===!1:!1;c=c||s===!1;const f=c?l:s;return f&&typeof f!="boolean"&&!Io(f)&&(Array.isArray(f)?f:[f]).forEach(g=>{const v=Fa(e,g);if(!v)return;const{transitionEnd:x,transition:T,...m}=v;for(const p in m){let h=m[p];if(Array.isArray(h)){const w=c?h.length-1:0;h=h[w]}h!==null&&(i[p]=h)}for(const p in x)i[p]=x[p]}),i}const Z=e=>e;class uc{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function eg(e){let t=new uc,n=new uc,r=0,i=!1,o=!1;const s=new WeakSet,l={schedule:(a,u=!1,c=!1)=>{const f=c&&i,d=f?t:n;return u&&s.add(a),d.add(a)&&f&&i&&(r=t.order.length),a},cancel:a=>{n.remove(a),s.delete(a)},process:a=>{if(i){o=!0;return}if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const c=t.order[u];c(a),s.has(c)&&(l.schedule(c),e())}i=!1,o&&(o=!1,l.process(a))}};return l}const ki=["prepare","read","update","preRender","render","postRender"],tg=40;function ng(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=ki.reduce((f,d)=>(f[d]=eg(()=>n=!0),f),{}),s=f=>o[f].process(i),l=()=>{const f=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(f-i.timestamp,tg),1),i.timestamp=f,i.isProcessing=!0,ki.forEach(s),i.isProcessing=!1,n&&t&&(r=!1,e(l))},a=()=>{n=!0,r=!0,i.isProcessing||e(l)};return{schedule:ki.reduce((f,d)=>{const g=o[d];return f[d]=(v,x=!1,T=!1)=>(n||a(),g.schedule(v,x,T)),f},{}),cancel:f=>ki.forEach(d=>o[d].cancel(f)),state:i,steps:o}}const{schedule:$,cancel:vt,state:he,steps:hs}=ng(typeof requestAnimationFrame<"u"?requestAnimationFrame:Z,!0),rg={useVisualState:zp({scrapeMotionValuesFromProps:Op,createRenderState:Rp,onMount:(e,t,{renderState:n,latestValues:r})=>{$.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),$.render(()=>{Ra(n,r,{enableHardwareAcceleration:!1},_a(t.tagName),e.transformTemplate),Fp(t,n)})}})},ig={useVisualState:zp({scrapeMotionValuesFromProps:Ia,createRenderState:Da})};function og(e,{forwardMotionProps:t=!1},n,r){return{...Ma(e)?rg:ig,preloadedFeatures:n,useRender:Qy(t),createVisualElement:r,Component:e}}function ct(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const Bp=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function zo(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const sg=e=>t=>Bp(t)&&e(t,zo(t));function dt(e,t,n,r){return ct(e,t,sg(n),r)}const lg=(e,t)=>n=>t(e(n)),Ot=(...e)=>e.reduce(lg);function bp(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const cc=bp("dragHorizontal"),fc=bp("dragVertical");function Up(e){let t=!1;if(e==="y")t=fc();else if(e==="x")t=cc();else{const n=cc(),r=fc();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function $p(){const e=Up(!0);return e?(e(),!1):!0}class Kt{constructor(t){this.isMounted=!1,this.node=t}update(){}}function dc(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),i=(o,s)=>{if(o.pointerType==="touch"||$p())return;const l=e.getProps();e.animationState&&l.whileHover&&e.animationState.setActive("whileHover",t),l[r]&&$.update(()=>l[r](o,s))};return dt(e.current,n,i,{passive:!e.getProps()[r]})}class ag extends Kt{mount(){this.unmount=Ot(dc(this.node,!0),dc(this.node,!1))}unmount(){}}class ug extends Kt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Ot(ct(this.node.current,"focus",()=>this.onFocus()),ct(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Hp=(e,t)=>t?e===t?!0:Hp(e,t.parentElement):!1;function ms(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,zo(n))}class cg extends Kt{constructor(){super(...arguments),this.removeStartListeners=Z,this.removeEndListeners=Z,this.removeAccessibleListeners=Z,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),o=dt(window,"pointerup",(l,a)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:c,globalTapTarget:f}=this.node.getProps();$.update(()=>{!f&&!Hp(this.node.current,l.target)?c&&c(l,a):u&&u(l,a)})},{passive:!(r.onTap||r.onPointerUp)}),s=dt(window,"pointercancel",(l,a)=>this.cancelPress(l,a),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Ot(o,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=o=>{if(o.key!=="Enter"||this.isPressing)return;const s=l=>{l.key!=="Enter"||!this.checkPressEnd()||ms("up",(a,u)=>{const{onTap:c}=this.node.getProps();c&&$.update(()=>c(a,u))})};this.removeEndListeners(),this.removeEndListeners=ct(this.node.current,"keyup",s),ms("down",(l,a)=>{this.startPress(l,a)})},n=ct(this.node.current,"keydown",t),r=()=>{this.isPressing&&ms("cancel",(o,s)=>this.cancelPress(o,s))},i=ct(this.node.current,"blur",r);this.removeAccessibleListeners=Ot(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&$.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!$p()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&$.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=dt(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=ct(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Ot(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const wl=new WeakMap,ys=new WeakMap,fg=e=>{const t=wl.get(e.target);t&&t(e)},dg=e=>{e.forEach(fg)};function pg({root:e,...t}){const n=e||document;ys.has(n)||ys.set(n,{});const r=ys.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(dg,{root:e,...t})),r[i]}function hg(e,t,n){const r=pg(t);return wl.set(e,n),r.observe(e),()=>{wl.delete(e),r.unobserve(e)}}const mg={some:0,all:1};class yg extends Kt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:o}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:mg[i]},l=a=>{const{isIntersecting:u}=a;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:f}=this.node.getProps(),d=u?c:f;d&&d(a)};return hg(this.node.current,s,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(gg(t,n))&&this.startObserver()}unmount(){}}function gg({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const vg={inView:{Feature:yg},tap:{Feature:cg},focus:{Feature:ug},hover:{Feature:ag}};function Wp(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function xg(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function wg(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function Bo(e,t,n){const r=e.getProps();return Fa(r,t,n!==void 0?n:r.custom,xg(e),wg(e))}let Sg=Z,Oa=Z;const zt=e=>e*1e3,pt=e=>e/1e3,kg={current:!1},Gp=e=>Array.isArray(e)&&typeof e[0]=="number";function Kp(e){return!!(!e||typeof e=="string"&&Qp[e]||Gp(e)||Array.isArray(e)&&e.every(Kp))}const hr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Qp={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:hr([0,.65,.55,1]),circOut:hr([.55,0,1,.45]),backIn:hr([.31,.01,.66,-.59]),backOut:hr([.33,1.53,.69,.99])};function Yp(e){if(e)return Gp(e)?hr(e):Array.isArray(e)?e.map(Yp):Qp[e]}function Pg(e,t,n,{delay:r=0,duration:i,repeat:o=0,repeatType:s="loop",ease:l,times:a}={}){const u={[t]:n};a&&(u.offset=a);const c=Yp(l);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}function Cg(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const Xp=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,Tg=1e-7,Eg=12;function jg(e,t,n,r,i){let o,s,l=0;do s=t+(n-t)/2,o=Xp(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>Tg&&++l<Eg);return s}function ni(e,t,n,r){if(e===t&&n===r)return Z;const i=o=>jg(o,0,1,e,n);return o=>o===0||o===1?o:Xp(i(o),t,r)}const Ng=ni(.42,0,1,1),Vg=ni(0,0,.58,1),Zp=ni(.42,0,.58,1),Lg=e=>Array.isArray(e)&&typeof e[0]!="number",qp=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Jp=e=>t=>1-e(1-t),za=e=>1-Math.sin(Math.acos(e)),eh=Jp(za),Mg=qp(za),th=ni(.33,1.53,.69,.99),Ba=Jp(th),Ag=qp(Ba),Dg=e=>(e*=2)<1?.5*Ba(e):.5*(2-Math.pow(2,-10*(e-1))),Rg={linear:Z,easeIn:Ng,easeInOut:Zp,easeOut:Vg,circIn:za,circInOut:Mg,circOut:eh,backIn:Ba,backInOut:Ag,backOut:th,anticipate:Dg},pc=e=>{if(Array.isArray(e)){Oa(e.length===4);const[t,n,r,i]=e;return ni(t,n,r,i)}else if(typeof e=="string")return Rg[e];return e},ba=(e,t)=>n=>!!(ei(n)&&Ry.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),nh=(e,t,n)=>r=>{if(!ei(r))return r;const[i,o,s,l]=r.match(Oo);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:l!==void 0?parseFloat(l):1}},_g=e=>Ut(0,255,e),gs={...gn,transform:e=>Math.round(_g(e))},sn={test:ba("rgb","red"),parse:nh("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+gs.transform(e)+", "+gs.transform(t)+", "+gs.transform(n)+", "+Tr(Cr.transform(r))+")"};function Ig(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Sl={test:ba("#"),parse:Ig,transform:sn.transform},Dn={test:ba("hsl","hue"),parse:nh("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+ot.transform(Tr(t))+", "+ot.transform(Tr(n))+", "+Tr(Cr.transform(r))+")"},ve={test:e=>sn.test(e)||Sl.test(e)||Dn.test(e),parse:e=>sn.test(e)?sn.parse(e):Dn.test(e)?Dn.parse(e):Sl.parse(e),transform:e=>ei(e)?e:e.hasOwnProperty("red")?sn.transform(e):Dn.transform(e)},K=(e,t,n)=>-n*e+n*t+e;function vs(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Fg({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;i=vs(a,l,e+1/3),o=vs(a,l,e),s=vs(a,l,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}const xs=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},Og=[Sl,sn,Dn],zg=e=>Og.find(t=>t.test(e));function hc(e){const t=zg(e);let n=t.parse(e);return t===Dn&&(n=Fg(n)),n}const rh=(e,t)=>{const n=hc(e),r=hc(t),i={...n};return o=>(i.red=xs(n.red,r.red,o),i.green=xs(n.green,r.green,o),i.blue=xs(n.blue,r.blue,o),i.alpha=K(n.alpha,r.alpha,o),sn.transform(i))};function Bg(e){var t,n;return isNaN(e)&&ei(e)&&(((t=e.match(Oo))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Lp))===null||n===void 0?void 0:n.length)||0)>0}const ih={regex:Ay,countKey:"Vars",token:"${v}",parse:Z},oh={regex:Lp,countKey:"Colors",token:"${c}",parse:ve.parse},sh={regex:Oo,countKey:"Numbers",token:"${n}",parse:gn.parse};function ws(e,{regex:t,countKey:n,token:r,parse:i}){const o=e.tokenised.match(t);o&&(e["num"+n]=o.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...o.map(i)))}function po(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&ws(n,ih),ws(n,oh),ws(n,sh),n}function lh(e){return po(e).values}function ah(e){const{values:t,numColors:n,numVars:r,tokenised:i}=po(e),o=t.length;return s=>{let l=i;for(let a=0;a<o;a++)a<r?l=l.replace(ih.token,s[a]):a<r+n?l=l.replace(oh.token,ve.transform(s[a])):l=l.replace(sh.token,Tr(s[a]));return l}}const bg=e=>typeof e=="number"?0:e;function Ug(e){const t=lh(e);return ah(e)(t.map(bg))}const $t={test:Bg,parse:lh,createTransformer:ah,getAnimatableNone:Ug},uh=(e,t)=>n=>`${n>0?t:e}`;function ch(e,t){return typeof e=="number"?n=>K(e,t,n):ve.test(e)?rh(e,t):e.startsWith("var(")?uh(e,t):dh(e,t)}const fh=(e,t)=>{const n=[...e],r=n.length,i=e.map((o,s)=>ch(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}},$g=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=ch(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}},dh=(e,t)=>{const n=$t.createTransformer(t),r=po(e),i=po(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?Ot(fh(r.values,i.values),n):uh(e,t)},Kr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},mc=(e,t)=>n=>K(e,t,n);function Hg(e){return typeof e=="number"?mc:typeof e=="string"?ve.test(e)?rh:dh:Array.isArray(e)?fh:typeof e=="object"?$g:mc}function Wg(e,t,n){const r=[],i=n||Hg(e[0]),o=e.length-1;for(let s=0;s<o;s++){let l=i(e[s],e[s+1]);if(t){const a=Array.isArray(t)?t[s]||Z:t;l=Ot(a,l)}r.push(l)}return r}function ph(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;if(Oa(o===t.length),o===1)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=Wg(t,r,i),l=s.length,a=u=>{let c=0;if(l>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const f=Kr(e[c],e[c+1],u);return s[c](f)};return n?u=>a(Ut(e[0],e[o-1],u)):a}function Gg(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Kr(0,t,r);e.push(K(n,1,i))}}function Kg(e){const t=[0];return Gg(t,e.length-1),t}function Qg(e,t){return e.map(n=>n*t)}function Yg(e,t){return e.map(()=>t||Zp).splice(0,e.length-1)}function ho({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=Lg(r)?r.map(pc):pc(r),o={done:!1,value:t[0]},s=Qg(n&&n.length===t.length?n:Kg(t),e),l=ph(s,t,{ease:Array.isArray(i)?i:Yg(t,i)});return{calculatedDuration:e,next:a=>(o.value=l(a),o.done=a>=e,o)}}function hh(e,t){return t?e*(1e3/t):0}const Xg=5;function mh(e,t,n){const r=Math.max(t-Xg,0);return hh(n-e(r),t-r)}const Ss=.001,Zg=.01,yc=10,qg=.05,Jg=1;function ev({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o;Sg(e<=zt(yc));let s=1-t;s=Ut(qg,Jg,s),e=Ut(Zg,yc,pt(e)),s<1?(i=u=>{const c=u*s,f=c*e,d=c-n,g=kl(u,s),v=Math.exp(-f);return Ss-d/g*v},o=u=>{const f=u*s*e,d=f*n+n,g=Math.pow(s,2)*Math.pow(u,2)*e,v=Math.exp(-f),x=kl(Math.pow(u,2),s);return(-i(u)+Ss>0?-1:1)*((d-g)*v)/x}):(i=u=>{const c=Math.exp(-u*e),f=(u-n)*e+1;return-Ss+c*f},o=u=>{const c=Math.exp(-u*e),f=(n-u)*(e*e);return c*f});const l=5/e,a=nv(i,o,l);if(e=zt(e),isNaN(a))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(a,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const tv=12;function nv(e,t,n){let r=n;for(let i=1;i<tv;i++)r=r-e(r)/t(r);return r}function kl(e,t){return e*Math.sqrt(1-t*t)}const rv=["duration","bounce"],iv=["stiffness","damping","mass"];function gc(e,t){return t.some(n=>e[n]!==void 0)}function ov(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!gc(e,iv)&&gc(e,rv)){const n=ev(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function yh({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],o=e[e.length-1],s={done:!1,value:i},{stiffness:l,damping:a,mass:u,duration:c,velocity:f,isResolvedFromDuration:d}=ov({...r,velocity:-pt(r.velocity||0)}),g=f||0,v=a/(2*Math.sqrt(l*u)),x=o-i,T=pt(Math.sqrt(l/u)),m=Math.abs(x)<5;n||(n=m?.01:2),t||(t=m?.005:.5);let p;if(v<1){const h=kl(T,v);p=w=>{const S=Math.exp(-v*T*w);return o-S*((g+v*T*x)/h*Math.sin(h*w)+x*Math.cos(h*w))}}else if(v===1)p=h=>o-Math.exp(-T*h)*(x+(g+T*x)*h);else{const h=T*Math.sqrt(v*v-1);p=w=>{const S=Math.exp(-v*T*w),E=Math.min(h*w,300);return o-S*((g+v*T*x)*Math.sinh(E)+h*x*Math.cosh(E))/h}}return{calculatedDuration:d&&c||null,next:h=>{const w=p(h);if(d)s.done=h>=c;else{let S=g;h!==0&&(v<1?S=mh(p,h,w):S=0);const E=Math.abs(S)<=n,C=Math.abs(o-w)<=t;s.done=E&&C}return s.value=s.done?o:w,s}}}function vc({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:l,max:a,restDelta:u=.5,restSpeed:c}){const f=e[0],d={done:!1,value:f},g=P=>l!==void 0&&P<l||a!==void 0&&P>a,v=P=>l===void 0?a:a===void 0||Math.abs(l-P)<Math.abs(a-P)?l:a;let x=n*t;const T=f+x,m=s===void 0?T:s(T);m!==T&&(x=m-f);const p=P=>-x*Math.exp(-P/r),h=P=>m+p(P),w=P=>{const _=p(P),A=h(P);d.done=Math.abs(_)<=u,d.value=d.done?m:A};let S,E;const C=P=>{g(d.value)&&(S=P,E=yh({keyframes:[d.value,v(d.value)],velocity:mh(h,P,d.value),damping:i,stiffness:o,restDelta:u,restSpeed:c}))};return C(0),{calculatedDuration:null,next:P=>{let _=!1;return!E&&S===void 0&&(_=!0,w(P),C(P)),S!==void 0&&P>S?E.next(P-S):(!_&&w(P),d)}}}const sv=e=>{const t=({timestamp:n})=>e(n);return{start:()=>$.update(t,!0),stop:()=>vt(t),now:()=>he.isProcessing?he.timestamp:performance.now()}},xc=2e4;function wc(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<xc;)t+=n,r=e.next(t);return t>=xc?1/0:t}const lv={decay:vc,inertia:vc,tween:ho,keyframes:ho,spring:yh};function mo({autoplay:e=!0,delay:t=0,driver:n=sv,keyframes:r,type:i="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:l="loop",onPlay:a,onStop:u,onComplete:c,onUpdate:f,...d}){let g=1,v=!1,x,T;const m=()=>{T=new Promise(D=>{x=D})};m();let p;const h=lv[i]||ho;let w;h!==ho&&typeof r[0]!="number"&&(w=ph([0,100],r,{clamp:!1}),r=[0,100]);const S=h({...d,keyframes:r});let E;l==="mirror"&&(E=h({...d,keyframes:[...r].reverse(),velocity:-(d.velocity||0)}));let C="idle",P=null,_=null,A=null;S.calculatedDuration===null&&o&&(S.calculatedDuration=wc(S));const{calculatedDuration:ie}=S;let ae=1/0,ge=1/0;ie!==null&&(ae=ie+s,ge=ae*(o+1)-s);let oe=0;const wt=D=>{if(_===null)return;g>0&&(_=Math.min(_,D)),g<0&&(_=Math.min(D-ge/g,_)),P!==null?oe=P:oe=Math.round(D-_)*g;const H=oe-t*(g>=0?1:-1),Qt=g>=0?H<0:H>ge;oe=Math.max(H,0),C==="finished"&&P===null&&(oe=ge);let Je=oe,vn=S;if(o){const bo=Math.min(oe,ge)/ae;let ri=Math.floor(bo),Xt=bo%1;!Xt&&bo>=1&&(Xt=1),Xt===1&&ri--,ri=Math.min(ri,o+1),!!(ri%2)&&(l==="reverse"?(Xt=1-Xt,s&&(Xt-=s/ae)):l==="mirror"&&(vn=E)),Je=Ut(0,1,Xt)*ae}const Ve=Qt?{done:!1,value:r[0]}:vn.next(Je);w&&(Ve.value=w(Ve.value));let{done:Yt}=Ve;!Qt&&ie!==null&&(Yt=g>=0?oe>=ge:oe<=0);const Uh=P===null&&(C==="finished"||C==="running"&&Yt);return f&&f(Ve.value),Uh&&j(),Ve},q=()=>{p&&p.stop(),p=void 0},Fe=()=>{C="idle",q(),x(),m(),_=A=null},j=()=>{C="finished",c&&c(),q(),x()},M=()=>{if(v)return;p||(p=n(wt));const D=p.now();a&&a(),P!==null?_=D-P:(!_||C==="finished")&&(_=D),C==="finished"&&m(),A=_,P=null,C="running",p.start()};e&&M();const I={then(D,H){return T.then(D,H)},get time(){return pt(oe)},set time(D){D=zt(D),oe=D,P!==null||!p||g===0?P=D:_=p.now()-D/g},get duration(){const D=S.calculatedDuration===null?wc(S):S.calculatedDuration;return pt(D)},get speed(){return g},set speed(D){D===g||!p||(g=D,I.time=pt(oe))},get state(){return C},play:M,pause:()=>{C="paused",P=oe},stop:()=>{v=!0,C!=="idle"&&(C="idle",u&&u(),Fe())},cancel:()=>{A!==null&&wt(A),Fe()},complete:()=>{C="finished"},sample:D=>(_=0,wt(D))};return I}function av(e){let t;return()=>(t===void 0&&(t=e()),t)}const uv=av(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),cv=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),Pi=10,fv=2e4,dv=(e,t)=>t.type==="spring"||e==="backgroundColor"||!Kp(t.ease);function pv(e,t,{onUpdate:n,onComplete:r,...i}){if(!(uv()&&cv.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let s=!1,l,a,u=!1;const c=()=>{a=new Promise(h=>{l=h})};c();let{keyframes:f,duration:d=300,ease:g,times:v}=i;if(dv(t,i)){const h=mo({...i,repeat:0,delay:0});let w={done:!1,value:f[0]};const S=[];let E=0;for(;!w.done&&E<fv;)w=h.sample(E),S.push(w.value),E+=Pi;v=void 0,f=S,d=E-Pi,g="linear"}const x=Pg(e.owner.current,t,f,{...i,duration:d,ease:g,times:v}),T=()=>{u=!1,x.cancel()},m=()=>{u=!0,$.update(T),l(),c()};return x.onfinish=()=>{u||(e.set(Cg(f,i)),r&&r(),m())},{then(h,w){return a.then(h,w)},attachTimeline(h){return x.timeline=h,x.onfinish=null,Z},get time(){return pt(x.currentTime||0)},set time(h){x.currentTime=zt(h)},get speed(){return x.playbackRate},set speed(h){x.playbackRate=h},get duration(){return pt(d)},play:()=>{s||(x.play(),vt(T))},pause:()=>x.pause(),stop:()=>{if(s=!0,x.playState==="idle")return;const{currentTime:h}=x;if(h){const w=mo({...i,autoplay:!1});e.setWithVelocity(w.sample(h-Pi).value,w.sample(h).value,Pi)}m()},complete:()=>{u||x.finish()},cancel:m}}function hv({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:Z,pause:Z,stop:Z,then:o=>(o(),Promise.resolve()),cancel:Z,complete:Z});return t?mo({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}const mv={type:"spring",stiffness:500,damping:25,restSpeed:10},yv=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),gv={type:"keyframes",duration:.8},vv={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},xv=(e,{keyframes:t})=>t.length>2?gv:yn.has(e)?e.startsWith("scale")?yv(t[1]):mv:vv,Pl=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&($t.test(t)||t==="0")&&!t.startsWith("url(")),wv=new Set(["brightness","contrast","saturate","opacity"]);function Sv(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Oo)||[];if(!r)return e;const i=n.replace(r,"");let o=wv.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const kv=/([a-z-]*)\(.*?\)/g,Cl={...$t,getAnimatableNone:e=>{const t=e.match(kv);return t?t.map(Sv).join(" "):e}},Pv={...Mp,color:ve,backgroundColor:ve,outlineColor:ve,fill:ve,stroke:ve,borderColor:ve,borderTopColor:ve,borderRightColor:ve,borderBottomColor:ve,borderLeftColor:ve,filter:Cl,WebkitFilter:Cl},Ua=e=>Pv[e];function gh(e,t){let n=Ua(e);return n!==Cl&&(n=$t),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const vh=e=>/^0[^.\s]+$/.test(e);function Cv(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||vh(e)}function Tv(e,t,n,r){const i=Pl(t,n);let o;Array.isArray(n)?o=[...n]:o=[null,n];const s=r.from!==void 0?r.from:e.get();let l;const a=[];for(let u=0;u<o.length;u++)o[u]===null&&(o[u]=u===0?s:o[u-1]),Cv(o[u])&&a.push(u),typeof o[u]=="string"&&o[u]!=="none"&&o[u]!=="0"&&(l=o[u]);if(i&&a.length&&l)for(let u=0;u<a.length;u++){const c=a[u];o[c]=gh(t,l)}return o}function Ev({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:l,from:a,elapsed:u,...c}){return!!Object.keys(c).length}function $a(e,t){return e[t]||e.default||e}const jv={skipAnimations:!1},Ha=(e,t,n,r={})=>i=>{const o=$a(r,e)||{},s=o.delay||r.delay||0;let{elapsed:l=0}=r;l=l-zt(s);const a=Tv(t,e,n,o),u=a[0],c=a[a.length-1],f=Pl(e,u),d=Pl(e,c);let g={keyframes:a,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-l,onUpdate:v=>{t.set(v),o.onUpdate&&o.onUpdate(v)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(Ev(o)||(g={...g,...xv(e,g)}),g.duration&&(g.duration=zt(g.duration)),g.repeatDelay&&(g.repeatDelay=zt(g.repeatDelay)),!f||!d||kg.current||o.type===!1||jv.skipAnimations)return hv(g);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const v=pv(t,e,g);if(v)return v}return mo(g)};function yo(e){return!!(Ne(e)&&e.add)}const xh=e=>/^\-?\d*\.?\d+$/.test(e);function Wa(e,t){e.indexOf(t)===-1&&e.push(t)}function Ga(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Ka{constructor(){this.subscriptions=[]}add(t){return Wa(this.subscriptions,t),()=>Ga(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Nv=e=>!isNaN(parseFloat(e));class Vv{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:o,timestamp:s}=he;this.lastUpdated!==s&&(this.timeDelta=o,this.lastUpdated=s,$.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>$.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=Nv(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Ka);const r=this.events[t].add(n);return t==="change"?()=>{r(),$.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?hh(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Yn(e,t){return new Vv(e,t)}const wh=e=>t=>t.test(e),Lv={test:e=>e==="auto",parse:e=>e},Sh=[gn,L,ot,kt,Iy,_y,Lv],ar=e=>Sh.find(wh(e)),Mv=[...Sh,ve,$t],Av=e=>Mv.find(wh(e));function Dv(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Yn(n))}function Rv(e,t){const n=Bo(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const s in o){const l=Zy(o[s]);Dv(e,s,l)}}function _v(e,t,n){var r,i;const o=Object.keys(t).filter(l=>!e.hasValue(l)),s=o.length;if(s)for(let l=0;l<s;l++){const a=o[l],u=t[a];let c=null;Array.isArray(u)&&(c=u[0]),c===null&&(c=(i=(r=n[a])!==null&&r!==void 0?r:e.readValue(a))!==null&&i!==void 0?i:t[a]),c!=null&&(typeof c=="string"&&(xh(c)||vh(c))?c=parseFloat(c):!Av(c)&&$t.test(u)&&(c=gh(a,u)),e.addValue(a,Yn(c,{owner:e})),n[a]===void 0&&(n[a]=c),c!==null&&e.setBaseTarget(a,c))}}function Iv(e,t){return t?(t[e]||t.default||t).from:void 0}function Fv(e,t,n){const r={};for(const i in e){const o=Iv(i,t);if(o!==void 0)r[i]=o;else{const s=n.getValue(i);s&&(r[i]=s.get())}}return r}function Ov({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function zv(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function kh(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...l}=e.makeTargetAnimatable(t);const a=e.getValue("willChange");r&&(o=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const f in l){const d=e.getValue(f),g=l[f];if(!d||g===void 0||c&&Ov(c,f))continue;const v={delay:n,elapsed:0,...$a(o||{},f)};if(window.HandoffAppearAnimations){const m=e.getProps()[Pp];if(m){const p=window.HandoffAppearAnimations(m,f,d,$);p!==null&&(v.elapsed=p,v.isHandoff=!0)}}let x=!v.isHandoff&&!zv(d,g);if(v.type==="spring"&&(d.getVelocity()||v.velocity)&&(x=!1),d.animation&&(x=!1),x)continue;d.start(Ha(f,d,g,e.shouldReduceMotion&&yn.has(f)?{type:!1}:v));const T=d.animation;yo(a)&&(a.add(f),T.then(()=>a.remove(f))),u.push(T)}return s&&Promise.all(u).then(()=>{s&&Rv(e,s)}),u}function Tl(e,t,n={}){const r=Bo(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const o=r?()=>Promise.all(kh(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(a=0)=>{const{delayChildren:u=0,staggerChildren:c,staggerDirection:f}=i;return Bv(e,t,u+a,c,f,n)}:()=>Promise.resolve(),{when:l}=i;if(l){const[a,u]=l==="beforeChildren"?[o,s]:[s,o];return a().then(()=>u())}else return Promise.all([o(),s(n.delay)])}function Bv(e,t,n=0,r=0,i=1,o){const s=[],l=(e.variantChildren.size-1)*r,a=i===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(bv).forEach((u,c)=>{u.notify("AnimationStart",t),s.push(Tl(u,t,{...o,delay:n+a(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function bv(e,t){return e.sortNodePosition(t)}function Uv(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>Tl(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=Tl(e,t,n);else{const i=typeof t=="function"?Bo(e,t,n.custom):t;r=Promise.all(kh(e,i,n))}return r.then(()=>e.notify("AnimationComplete",t))}const $v=[...Va].reverse(),Hv=Va.length;function Wv(e){return t=>Promise.all(t.map(({animation:n,options:r})=>Uv(e,n,r)))}function Gv(e){let t=Wv(e);const n=Qv();let r=!0;const i=(a,u)=>{const c=Bo(e,u);if(c){const{transition:f,transitionEnd:d,...g}=c;a={...a,...g,...d}}return a};function o(a){t=a(e)}function s(a,u){const c=e.getProps(),f=e.getVariantContext(!0)||{},d=[],g=new Set;let v={},x=1/0;for(let m=0;m<Hv;m++){const p=$v[m],h=n[p],w=c[p]!==void 0?c[p]:f[p],S=Wr(w),E=p===u?h.isActive:null;E===!1&&(x=m);let C=w===f[p]&&w!==c[p]&&S;if(C&&r&&e.manuallyAnimateOnMount&&(C=!1),h.protectedKeys={...v},!h.isActive&&E===null||!w&&!h.prevProp||Io(w)||typeof w=="boolean")continue;let _=Kv(h.prevProp,w)||p===u&&h.isActive&&!C&&S||m>x&&S,A=!1;const ie=Array.isArray(w)?w:[w];let ae=ie.reduce(i,{});E===!1&&(ae={});const{prevResolvedValues:ge={}}=h,oe={...ge,...ae},wt=q=>{_=!0,g.has(q)&&(A=!0,g.delete(q)),h.needsAnimating[q]=!0};for(const q in oe){const Fe=ae[q],j=ge[q];if(v.hasOwnProperty(q))continue;let M=!1;fo(Fe)&&fo(j)?M=!Wp(Fe,j):M=Fe!==j,M?Fe!==void 0?wt(q):g.add(q):Fe!==void 0&&g.has(q)?wt(q):h.protectedKeys[q]=!0}h.prevProp=w,h.prevResolvedValues=ae,h.isActive&&(v={...v,...ae}),r&&e.blockInitialAnimation&&(_=!1),_&&(!C||A)&&d.push(...ie.map(q=>({animation:q,options:{type:p,...a}})))}if(g.size){const m={};g.forEach(p=>{const h=e.getBaseTarget(p);h!==void 0&&(m[p]=h)}),d.push({animation:m})}let T=!!d.length;return r&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(T=!1),r=!1,T?t(d):Promise.resolve()}function l(a,u,c){var f;if(n[a].isActive===u)return Promise.resolve();(f=e.variantChildren)===null||f===void 0||f.forEach(g=>{var v;return(v=g.animationState)===null||v===void 0?void 0:v.setActive(a,u)}),n[a].isActive=u;const d=s(c,a);for(const g in n)n[g].protectedKeys={};return d}return{animateChanges:s,setActive:l,setAnimateFunction:o,getState:()=>n}}function Kv(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Wp(t,e):!1}function Zt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Qv(){return{animate:Zt(!0),whileInView:Zt(),whileHover:Zt(),whileTap:Zt(),whileDrag:Zt(),whileFocus:Zt(),exit:Zt()}}class Yv extends Kt{constructor(t){super(t),t.animationState||(t.animationState=Gv(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),Io(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let Xv=0;class Zv extends Kt{constructor(){super(...arguments),this.id=Xv++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const o=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&o.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const qv={animation:{Feature:Yv},exit:{Feature:Zv}},Sc=(e,t)=>Math.abs(e-t);function Jv(e,t){const n=Sc(e.x,t.x),r=Sc(e.y,t.y);return Math.sqrt(n**2+r**2)}class Ph{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const f=Ps(this.lastMoveEventInfo,this.history),d=this.startEvent!==null,g=Jv(f.offset,{x:0,y:0})>=3;if(!d&&!g)return;const{point:v}=f,{timestamp:x}=he;this.history.push({...v,timestamp:x});const{onStart:T,onMove:m}=this.handlers;d||(T&&T(this.lastMoveEvent,f),this.startEvent=this.lastMoveEvent),m&&m(this.lastMoveEvent,f)},this.handlePointerMove=(f,d)=>{this.lastMoveEvent=f,this.lastMoveEventInfo=ks(d,this.transformPagePoint),$.update(this.updatePoint,!0)},this.handlePointerUp=(f,d)=>{this.end();const{onEnd:g,onSessionEnd:v,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const T=Ps(f.type==="pointercancel"?this.lastMoveEventInfo:ks(d,this.transformPagePoint),this.history);this.startEvent&&g&&g(f,T),v&&v(f,T)},!Bp(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const s=zo(t),l=ks(s,this.transformPagePoint),{point:a}=l,{timestamp:u}=he;this.history=[{...a,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,Ps(l,this.history)),this.removeListeners=Ot(dt(this.contextWindow,"pointermove",this.handlePointerMove),dt(this.contextWindow,"pointerup",this.handlePointerUp),dt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),vt(this.updatePoint)}}function ks(e,t){return t?{point:t(e.point)}:e}function kc(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Ps({point:e},t){return{point:e,delta:kc(e,Ch(t)),offset:kc(e,e1(t)),velocity:t1(t,.1)}}function e1(e){return e[0]}function Ch(e){return e[e.length-1]}function t1(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=Ch(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>zt(t)));)n--;if(!r)return{x:0,y:0};const o=pt(i.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function Re(e){return e.max-e.min}function El(e,t=0,n=.01){return Math.abs(e-t)<=n}function Pc(e,t,n,r=.5){e.origin=r,e.originPoint=K(t.min,t.max,e.origin),e.scale=Re(n)/Re(t),(El(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=K(n.min,n.max,e.origin)-e.originPoint,(El(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Er(e,t,n,r){Pc(e.x,t.x,n.x,r?r.originX:void 0),Pc(e.y,t.y,n.y,r?r.originY:void 0)}function Cc(e,t,n){e.min=n.min+t.min,e.max=e.min+Re(t)}function n1(e,t,n){Cc(e.x,t.x,n.x),Cc(e.y,t.y,n.y)}function Tc(e,t,n){e.min=t.min-n.min,e.max=e.min+Re(t)}function jr(e,t,n){Tc(e.x,t.x,n.x),Tc(e.y,t.y,n.y)}function r1(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?K(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?K(n,e,r.max):Math.min(e,n)),e}function Ec(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function i1(e,{top:t,left:n,bottom:r,right:i}){return{x:Ec(e.x,n,i),y:Ec(e.y,t,r)}}function jc(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function o1(e,t){return{x:jc(e.x,t.x),y:jc(e.y,t.y)}}function s1(e,t){let n=.5;const r=Re(e),i=Re(t);return i>r?n=Kr(t.min,t.max-r,e.min):r>i&&(n=Kr(e.min,e.max-i,t.min)),Ut(0,1,n)}function l1(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const jl=.35;function a1(e=jl){return e===!1?e=0:e===!0&&(e=jl),{x:Nc(e,"left","right"),y:Nc(e,"top","bottom")}}function Nc(e,t,n){return{min:Vc(e,t),max:Vc(e,n)}}function Vc(e,t){return typeof e=="number"?e:e[t]||0}const Lc=()=>({translate:0,scale:1,origin:0,originPoint:0}),Rn=()=>({x:Lc(),y:Lc()}),Mc=()=>({min:0,max:0}),ee=()=>({x:Mc(),y:Mc()});function ze(e){return[e("x"),e("y")]}function Th({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function u1({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function c1(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Cs(e){return e===void 0||e===1}function Nl({scale:e,scaleX:t,scaleY:n}){return!Cs(e)||!Cs(t)||!Cs(n)}function en(e){return Nl(e)||Eh(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Eh(e){return Ac(e.x)||Ac(e.y)}function Ac(e){return e&&e!=="0%"}function go(e,t,n){const r=e-n,i=t*r;return n+i}function Dc(e,t,n,r,i){return i!==void 0&&(e=go(e,i,r)),go(e,n,r)+t}function Vl(e,t=0,n=1,r,i){e.min=Dc(e.min,t,n,r,i),e.max=Dc(e.max,t,n,r,i)}function jh(e,{x:t,y:n}){Vl(e.x,t.translate,t.scale,t.originPoint),Vl(e.y,n.translate,n.scale,n.originPoint)}function f1(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let o,s;for(let l=0;l<i;l++){o=n[l],s=o.projectionDelta;const a=o.instance;a&&a.style&&a.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&_n(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,jh(e,s)),r&&en(o.latestValues)&&_n(e,o.latestValues))}t.x=Rc(t.x),t.y=Rc(t.y)}function Rc(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Tt(e,t){e.min=e.min+t,e.max=e.max+t}function _c(e,t,[n,r,i]){const o=t[i]!==void 0?t[i]:.5,s=K(e.min,e.max,o);Vl(e,t[n],t[r],s,t.scale)}const d1=["x","scaleX","originX"],p1=["y","scaleY","originY"];function _n(e,t){_c(e.x,t,d1),_c(e.y,t,p1)}function Nh(e,t){return Th(c1(e.getBoundingClientRect(),t))}function h1(e,t,n){const r=Nh(e,n),{scroll:i}=t;return i&&(Tt(r.x,i.offset.x),Tt(r.y,i.offset.y)),r}const Vh=({current:e})=>e?e.ownerDocument.defaultView:null,m1=new WeakMap;class y1{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ee(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:f}=this.getProps();f?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(zo(c,"page").point)},o=(c,f)=>{const{drag:d,dragPropagation:g,onDragStart:v}=this.getProps();if(d&&!g&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Up(d),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ze(T=>{let m=this.getAxisMotionValue(T).get()||0;if(ot.test(m)){const{projection:p}=this.visualElement;if(p&&p.layout){const h=p.layout.layoutBox[T];h&&(m=Re(h)*(parseFloat(m)/100))}}this.originPoint[T]=m}),v&&$.update(()=>v(c,f),!1,!0);const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},s=(c,f)=>{const{dragPropagation:d,dragDirectionLock:g,onDirectionLock:v,onDrag:x}=this.getProps();if(!d&&!this.openGlobalLock)return;const{offset:T}=f;if(g&&this.currentDirection===null){this.currentDirection=g1(T),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",f.point,T),this.updateAxis("y",f.point,T),this.visualElement.render(),x&&x(c,f)},l=(c,f)=>this.stop(c,f),a=()=>ze(c=>{var f;return this.getAnimationState(c)==="paused"&&((f=this.getAxisMotionValue(c).animation)===null||f===void 0?void 0:f.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Ph(t,{onSessionStart:i,onStart:o,onMove:s,onSessionEnd:l,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Vh(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&$.update(()=>o(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Ci(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=r1(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;n&&An(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=i1(i.layoutBox,n):this.constraints=!1,this.elastic=a1(r),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&ze(s=>{this.getAxisMotionValue(s)&&(this.constraints[s]=l1(i.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!An(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=h1(r,i.root,this.visualElement.getTransformPagePoint());let s=o1(i.layout.layoutBox,o);if(n){const l=n(u1(s));this.hasMutatedConstraints=!!l,l&&(s=Th(l))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},u=ze(c=>{if(!Ci(c,n,this.currentDirection))return;let f=a&&a[c]||{};s&&(f={min:0,max:0});const d=i?200:1e6,g=i?40:1e7,v={type:"inertia",velocity:r?t[c]:0,bounceStiffness:d,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...o,...f};return this.startAxisValueAnimation(c,v)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(Ha(t,r,0,n))}stopAnimation(){ze(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ze(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){ze(n=>{const{drag:r}=this.getProps();if(!Ci(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:l}=i.layout.layoutBox[n];o.set(t[n]-K(s,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!An(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};ze(s=>{const l=this.getAxisMotionValue(s);if(l){const a=l.get();i[s]=s1({min:a,max:a},this.constraints[s])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),ze(s=>{if(!Ci(s,t,null))return;const l=this.getAxisMotionValue(s),{min:a,max:u}=this.constraints[s];l.set(K(a,u,i[s]))})}addListeners(){if(!this.visualElement.current)return;m1.set(this.visualElement,this);const t=this.visualElement.current,n=dt(t,"pointerdown",a=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(a)}),r=()=>{const{dragConstraints:a}=this.getProps();An(a)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const s=ct(window,"resize",()=>this.scalePositionWithinConstraints()),l=i.addEventListener("didUpdate",({delta:a,hasLayoutChanged:u})=>{this.isDragging&&u&&(ze(c=>{const f=this.getAxisMotionValue(c);f&&(this.originPoint[c]+=a[c].translate,f.set(f.get()+a[c].translate))}),this.visualElement.render())});return()=>{s(),n(),o(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=jl,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:l}}}function Ci(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function g1(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class v1 extends Kt{constructor(t){super(t),this.removeGroupControls=Z,this.removeListeners=Z,this.controls=new y1(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Z}unmount(){this.removeGroupControls(),this.removeListeners()}}const Ic=e=>(t,n)=>{e&&$.update(()=>e(t,n))};class x1 extends Kt{constructor(){super(...arguments),this.removePointerDownListener=Z}onPointerDown(t){this.session=new Ph(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Vh(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Ic(t),onStart:Ic(n),onMove:r,onEnd:(o,s)=>{delete this.session,i&&$.update(()=>i(o,s))}}}mount(){this.removePointerDownListener=dt(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function w1(){const e=R.useContext(ja);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=R.useId();return R.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}const Oi={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Fc(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const ur={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(L.test(e))e=parseFloat(e);else return e;const n=Fc(e,t.target.x),r=Fc(e,t.target.y);return`${n}% ${r}%`}},S1={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=$t.parse(e);if(i.length>5)return r;const o=$t.createTransformer(e),s=typeof i[0]!="number"?1:0,l=n.x.scale*t.x,a=n.y.scale*t.y;i[0+s]/=l,i[1+s]/=a;const u=K(l,a,.5);return typeof i[2+s]=="number"&&(i[2+s]/=u),typeof i[3+s]=="number"&&(i[3+s]/=u),o(i)}};class k1 extends Il.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;Ny(P1),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Oi.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||$.postRender(()=>{const l=s.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Lh(e){const[t,n]=w1(),r=R.useContext(Tp);return Il.createElement(k1,{...e,layoutGroup:r,switchLayoutGroup:R.useContext(Ep),isPresent:t,safeToRemove:n})}const P1={borderRadius:{...ur,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ur,borderTopRightRadius:ur,borderBottomLeftRadius:ur,borderBottomRightRadius:ur,boxShadow:S1},Mh=["TopLeft","TopRight","BottomLeft","BottomRight"],C1=Mh.length,Oc=e=>typeof e=="string"?parseFloat(e):e,zc=e=>typeof e=="number"||L.test(e);function T1(e,t,n,r,i,o){i?(e.opacity=K(0,n.opacity!==void 0?n.opacity:1,E1(r)),e.opacityExit=K(t.opacity!==void 0?t.opacity:1,0,j1(r))):o&&(e.opacity=K(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<C1;s++){const l=`border${Mh[s]}Radius`;let a=Bc(t,l),u=Bc(n,l);if(a===void 0&&u===void 0)continue;a||(a=0),u||(u=0),a===0||u===0||zc(a)===zc(u)?(e[l]=Math.max(K(Oc(a),Oc(u),r),0),(ot.test(u)||ot.test(a))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=K(t.rotate||0,n.rotate||0,r))}function Bc(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const E1=Ah(0,.5,eh),j1=Ah(.5,.95,Z);function Ah(e,t,n){return r=>r<e?0:r>t?1:n(Kr(e,t,r))}function bc(e,t){e.min=t.min,e.max=t.max}function Oe(e,t){bc(e.x,t.x),bc(e.y,t.y)}function Uc(e,t,n,r,i){return e-=t,e=go(e,1/n,r),i!==void 0&&(e=go(e,1/i,r)),e}function N1(e,t=0,n=1,r=.5,i,o=e,s=e){if(ot.test(t)&&(t=parseFloat(t),t=K(s.min,s.max,t/100)-s.min),typeof t!="number")return;let l=K(o.min,o.max,r);e===o&&(l-=t),e.min=Uc(e.min,t,n,l,i),e.max=Uc(e.max,t,n,l,i)}function $c(e,t,[n,r,i],o,s){N1(e,t[n],t[r],t[i],t.scale,o,s)}const V1=["x","scaleX","originX"],L1=["y","scaleY","originY"];function Hc(e,t,n,r){$c(e.x,t,V1,n?n.x:void 0,r?r.x:void 0),$c(e.y,t,L1,n?n.y:void 0,r?r.y:void 0)}function Wc(e){return e.translate===0&&e.scale===1}function Dh(e){return Wc(e.x)&&Wc(e.y)}function M1(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Rh(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Gc(e){return Re(e.x)/Re(e.y)}class A1{constructor(){this.members=[]}add(t){Wa(this.members,t),t.scheduleRender()}remove(t){if(Ga(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Kc(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:a,rotateX:u,rotateY:c}=n;a&&(r+=`rotate(${a}deg) `),u&&(r+=`rotateX(${u}deg) `),c&&(r+=`rotateY(${c}deg) `)}const s=e.x.scale*t.x,l=e.y.scale*t.y;return(s!==1||l!==1)&&(r+=`scale(${s}, ${l})`),r||"none"}const D1=(e,t)=>e.depth-t.depth;class R1{constructor(){this.children=[],this.isDirty=!1}add(t){Wa(this.children,t),this.isDirty=!0}remove(t){Ga(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(D1),this.isDirty=!1,this.children.forEach(t)}}function _1(e,t){const n=performance.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(vt(r),e(o-t))};return $.read(r,!0),()=>vt(r)}function I1(e){window.MotionDebug&&window.MotionDebug.record(e)}function F1(e){return e instanceof SVGElement&&e.tagName!=="svg"}function O1(e,t,n){const r=Ne(e)?e:Yn(e);return r.start(Ha("",r,t,n)),r.animation}const Qc=["","X","Y","Z"],z1={visibility:"hidden"},Yc=1e3;let B1=0;const tn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function _h({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s={},l=t==null?void 0:t()){this.id=B1++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,tn.totalNodes=tn.resolvedTargetDeltas=tn.recalculatedProjection=0,this.nodes.forEach($1),this.nodes.forEach(Q1),this.nodes.forEach(Y1),this.nodes.forEach(H1),I1(tn)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new R1)}addEventListener(s,l){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new Ka),this.eventHandlers.get(s).add(l)}notifyListeners(s,...l){const a=this.eventHandlers.get(s);a&&a.notify(...l)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=F1(s),this.instance=s;const{layoutId:a,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(u||a)&&(this.isLayoutDirty=!0),e){let f;const d=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,f&&f(),f=_1(d,250),Oi.hasAnimatedSinceResize&&(Oi.hasAnimatedSinceResize=!1,this.nodes.forEach(Zc))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||u)&&this.addEventListener("didUpdate",({delta:f,hasLayoutChanged:d,hasRelativeTargetChanged:g,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||c.getDefaultTransition()||ex,{onLayoutAnimationStart:T,onLayoutAnimationComplete:m}=c.getProps(),p=!this.targetLayout||!Rh(this.targetLayout,v)||g,h=!d&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||h||d&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(f,h);const w={...$a(x,"layout"),onPlay:T,onComplete:m};(c.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w)}else d||Zc(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,vt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(X1),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const f=this.path[c];f.shouldResetTransform=!0,f.updateScroll("snapshot"),f.options.layoutRoot&&f.willUpdate(!1)}const{layoutId:l,layout:a}=this.options;if(l===void 0&&!a)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Xc);return}this.isUpdating||this.nodes.forEach(G1),this.isUpdating=!1,this.nodes.forEach(K1),this.nodes.forEach(b1),this.nodes.forEach(U1),this.clearAllSnapshots();const l=performance.now();he.delta=Ut(0,1e3/60,l-he.timestamp),he.timestamp=l,he.isProcessing=!0,hs.update.process(he),hs.preRender.process(he),hs.render.process(he),he.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(W1),this.sharedNodes.forEach(Z1)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,$.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){$.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ee(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let l=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(l=!1),l&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const s=this.isLayoutDirty||this.shouldResetTransform,l=this.projectionDelta&&!Dh(this.projectionDelta),a=this.getTransformTemplate(),u=a?a(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;s&&(l||en(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return s&&(a=this.removeTransform(a)),tx(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return ee();const l=s.measureViewportBox(),{scroll:a}=this.root;return a&&(Tt(l.x,a.offset.x),Tt(l.y,a.offset.y)),l}removeElementScroll(s){const l=ee();Oe(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a],{scroll:c,options:f}=u;if(u!==this.root&&c&&f.layoutScroll){if(c.isRoot){Oe(l,s);const{scroll:d}=this.root;d&&(Tt(l.x,-d.offset.x),Tt(l.y,-d.offset.y))}Tt(l.x,c.offset.x),Tt(l.y,c.offset.y)}}return l}applyTransform(s,l=!1){const a=ee();Oe(a,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];!l&&c.options.layoutScroll&&c.scroll&&c!==c.root&&_n(a,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),en(c.latestValues)&&_n(a,c.latestValues)}return en(this.latestValues)&&_n(a,this.latestValues),a}removeTransform(s){const l=ee();Oe(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a];if(!u.instance||!en(u.latestValues))continue;Nl(u.latestValues)&&u.updateSnapshot();const c=ee(),f=u.measurePageBox();Oe(c,f),Hc(l,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return en(this.latestValues)&&Hc(l,this.latestValues),l}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==he.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var l;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==a;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:f,layoutId:d}=this.options;if(!(!this.layout||!(f||d))){if(this.resolvedRelativeTargetAt=he.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ee(),this.relativeTargetOrigin=ee(),jr(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),Oe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ee(),this.targetWithTransforms=ee()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),n1(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Oe(this.target,this.layout.layoutBox),jh(this.target,this.targetDelta)):Oe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ee(),this.relativeTargetOrigin=ee(),jr(this.relativeTargetOrigin,this.target,g.target),Oe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}tn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Nl(this.parent.latestValues)||Eh(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const l=this.getLead(),a=!!this.resumingFrom||this!==l;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===he.timestamp&&(u=!1),u)return;const{layout:c,layoutId:f}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||f))return;Oe(this.layoutCorrected,this.layout.layoutBox);const d=this.treeScale.x,g=this.treeScale.y;f1(this.layoutCorrected,this.treeScale,this.path,a),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox);const{target:v}=l;if(!v){this.projectionTransform&&(this.projectionDelta=Rn(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=Rn(),this.projectionDeltaWithTransform=Rn());const x=this.projectionTransform;Er(this.projectionDelta,this.layoutCorrected,v,this.latestValues),this.projectionTransform=Kc(this.projectionDelta,this.treeScale),(this.projectionTransform!==x||this.treeScale.x!==d||this.treeScale.y!==g)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),tn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),s){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,l=!1){const a=this.snapshot,u=a?a.latestValues:{},c={...this.latestValues},f=Rn();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const d=ee(),g=a?a.source:void 0,v=this.layout?this.layout.source:void 0,x=g!==v,T=this.getStack(),m=!T||T.members.length<=1,p=!!(x&&!m&&this.options.crossfade===!0&&!this.path.some(J1));this.animationProgress=0;let h;this.mixTargetDelta=w=>{const S=w/1e3;qc(f.x,s.x,S),qc(f.y,s.y,S),this.setTargetDelta(f),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(jr(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),q1(this.relativeTarget,this.relativeTargetOrigin,d,S),h&&M1(this.relativeTarget,h)&&(this.isProjectionDirty=!1),h||(h=ee()),Oe(h,this.relativeTarget)),x&&(this.animationValues=c,T1(c,u,this.latestValues,S,p,m)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=S},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(vt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=$.update(()=>{Oi.hasAnimatedSinceResize=!0,this.currentAnimation=O1(0,Yc,{...s,onUpdate:l=>{this.mixTargetDelta(l),s.onUpdate&&s.onUpdate(l)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Yc),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:l,target:a,layout:u,latestValues:c}=s;if(!(!l||!a||!u)){if(this!==s&&this.layout&&u&&Ih(this.options.animationType,this.layout.layoutBox,u.layoutBox)){a=this.target||ee();const f=Re(this.layout.layoutBox.x);a.x.min=s.target.x.min,a.x.max=a.x.min+f;const d=Re(this.layout.layoutBox.y);a.y.min=s.target.y.min,a.y.max=a.y.min+d}Oe(l,a),_n(l,c),Er(this.projectionDeltaWithTransform,this.layoutCorrected,l,c)}}registerSharedNode(s,l){this.sharedNodes.has(s)||this.sharedNodes.set(s,new A1),this.sharedNodes.get(s).add(l);const u=l.options.initialPromotionConfig;l.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(l):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:l}=this.options;return l?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:l}=this.options;return l?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:l,preserveFollowOpacity:a}={}){const u=this.getStack();u&&u.promote(this,a),s&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let l=!1;const{latestValues:a}=s;if((a.rotate||a.rotateX||a.rotateY||a.rotateZ)&&(l=!0),!l)return;const u={};for(let c=0;c<Qc.length;c++){const f="rotate"+Qc[c];a[f]&&(u[f]=a[f],s.setStaticValue(f,0))}s.render();for(const c in u)s.setStaticValue(c,u[c]);s.scheduleRender()}getProjectionStyles(s){var l,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return z1;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Fi(s==null?void 0:s.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const f=this.getLead();if(!this.projectionDelta||!this.layout||!f.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=Fi(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!en(this.latestValues)&&(x.transform=c?c({},""):"none",this.hasProjected=!1),x}const d=f.animationValues||f.latestValues;this.applyTransformsToTarget(),u.transform=Kc(this.projectionDeltaWithTransform,this.treeScale,d),c&&(u.transform=c(d,u.transform));const{x:g,y:v}=this.projectionDelta;u.transformOrigin=`${g.origin*100}% ${v.origin*100}% 0`,f.animationValues?u.opacity=f===this?(a=(l=d.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:d.opacityExit:u.opacity=f===this?d.opacity!==void 0?d.opacity:"":d.opacityExit!==void 0?d.opacityExit:0;for(const x in uo){if(d[x]===void 0)continue;const{correct:T,applyTo:m}=uo[x],p=u.transform==="none"?d[x]:T(d[x],f);if(m){const h=m.length;for(let w=0;w<h;w++)u[m[w]]=p}else u[x]=p}return this.options.layoutId&&(u.pointerEvents=f===this?Fi(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var l;return(l=s.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Xc),this.root.sharedNodes.clear()}}}function b1(e){e.updateLayout()}function U1(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:o}=e.options,s=n.source!==e.layout.source;o==="size"?ze(f=>{const d=s?n.measuredBox[f]:n.layoutBox[f],g=Re(d);d.min=r[f].min,d.max=d.min+g}):Ih(o,n.layoutBox,r)&&ze(f=>{const d=s?n.measuredBox[f]:n.layoutBox[f],g=Re(r[f]);d.max=d.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[f].max=e.relativeTarget[f].min+g)});const l=Rn();Er(l,r,n.layoutBox);const a=Rn();s?Er(a,e.applyTransform(i,!0),n.measuredBox):Er(a,r,n.layoutBox);const u=!Dh(l);let c=!1;if(!e.resumeFrom){const f=e.getClosestProjectingParent();if(f&&!f.resumeFrom){const{snapshot:d,layout:g}=f;if(d&&g){const v=ee();jr(v,n.layoutBox,d.layoutBox);const x=ee();jr(x,r,g.layoutBox),Rh(v,x)||(c=!0),f.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=v,e.relativeParent=f)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:a,layoutDelta:l,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function $1(e){tn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function H1(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function W1(e){e.clearSnapshot()}function Xc(e){e.clearMeasurements()}function G1(e){e.isLayoutDirty=!1}function K1(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Zc(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Q1(e){e.resolveTargetDelta()}function Y1(e){e.calcProjection()}function X1(e){e.resetRotation()}function Z1(e){e.removeLeadSnapshot()}function qc(e,t,n){e.translate=K(t.translate,0,n),e.scale=K(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Jc(e,t,n,r){e.min=K(t.min,n.min,r),e.max=K(t.max,n.max,r)}function q1(e,t,n,r){Jc(e.x,t.x,n.x,r),Jc(e.y,t.y,n.y,r)}function J1(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const ex={duration:.45,ease:[.4,0,.1,1]},ef=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),tf=ef("applewebkit/")&&!ef("chrome/")?Math.round:Z;function nf(e){e.min=tf(e.min),e.max=tf(e.max)}function tx(e){nf(e.x),nf(e.y)}function Ih(e,t,n){return e==="position"||e==="preserve-aspect"&&!El(Gc(t),Gc(n),.2)}const nx=_h({attachResizeListener:(e,t)=>ct(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Ts={current:void 0},Fh=_h({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Ts.current){const e=new nx({});e.mount(window),e.setOptions({layoutScroll:!0}),Ts.current=e}return Ts.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),rx={pan:{Feature:x1},drag:{Feature:v1,ProjectionNode:Fh,MeasureLayout:Lh}},ix=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function ox(e){const t=ix.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Ll(e,t,n=1){const[r,i]=ox(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const s=o.trim();return xh(s)?parseFloat(s):s}else return xl(i)?Ll(i,t,n+1):i}function sx(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const o=i.get();if(!xl(o))return;const s=Ll(o,r);s&&i.set(s)});for(const i in t){const o=t[i];if(!xl(o))continue;const s=Ll(o,r);s&&(t[i]=s,n||(n={}),n[i]===void 0&&(n[i]=o))}return{target:t,transitionEnd:n}}const lx=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Oh=e=>lx.has(e),ax=e=>Object.keys(e).some(Oh),rf=e=>e===gn||e===L,of=(e,t)=>parseFloat(e.split(", ")[t]),sf=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return of(i[1],t);{const o=r.match(/^matrix\((.+)\)$/);return o?of(o[1],e):0}},ux=new Set(["x","y","z"]),cx=Jr.filter(e=>!ux.has(e));function fx(e){const t=[];return cx.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const Xn={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:sf(4,13),y:sf(5,14)};Xn.translateX=Xn.x;Xn.translateY=Xn.y;const dx=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,l={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{l[u]=Xn[u](r,o)}),t.render();const a=t.measureViewportBox();return n.forEach(u=>{const c=t.getValue(u);c&&c.jump(l[u]),e[u]=Xn[u](a,o)}),e},px=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(Oh);let o=[],s=!1;const l=[];if(i.forEach(a=>{const u=e.getValue(a);if(!e.hasValue(a))return;let c=n[a],f=ar(c);const d=t[a];let g;if(fo(d)){const v=d.length,x=d[0]===null?1:0;c=d[x],f=ar(c);for(let T=x;T<v&&d[T]!==null;T++)g?Oa(ar(d[T])===g):g=ar(d[T])}else g=ar(d);if(f!==g)if(rf(f)&&rf(g)){const v=u.get();typeof v=="string"&&u.set(parseFloat(v)),typeof d=="string"?t[a]=parseFloat(d):Array.isArray(d)&&g===L&&(t[a]=d.map(parseFloat))}else f!=null&&f.transform&&(g!=null&&g.transform)&&(c===0||d===0)?c===0?u.set(g.transform(c)):t[a]=f.transform(d):(s||(o=fx(e),s=!0),l.push(a),r[a]=r[a]!==void 0?r[a]:t[a],u.jump(d))}),l.length){const a=l.indexOf("height")>=0?window.pageYOffset:null,u=dx(t,e,l);return o.length&&o.forEach(([c,f])=>{e.getValue(c).set(f)}),e.render(),_o&&a!==null&&window.scrollTo({top:a}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function hx(e,t,n,r){return ax(t)?px(e,t,n,r):{target:t,transitionEnd:r}}const mx=(e,t,n,r)=>{const i=sx(e,t,r);return t=i.target,r=i.transitionEnd,hx(e,t,n,r)},Ml={current:null},zh={current:!1};function yx(){if(zh.current=!0,!!_o)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Ml.current=e.matches;e.addListener(t),t()}else Ml.current=!1}function gx(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],s=n[i];if(Ne(o))e.addValue(i,o),yo(r)&&r.add(i);else if(Ne(s))e.addValue(i,Yn(o,{owner:e})),yo(r)&&r.remove(i);else if(s!==o)if(e.hasValue(i)){const l=e.getValue(i);!l.hasAnimated&&l.set(o)}else{const l=e.getStaticValue(i);e.addValue(i,Yn(l!==void 0?l:o,{owner:e}))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const lf=new WeakMap,Bh=Object.keys(Gr),vx=Bh.length,af=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],xx=La.length;class wx{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>$.render(this.render,!1,!0);const{latestValues:l,renderState:a}=o;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=a,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=Fo(n),this.isVariantNode=Cp(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(n,{});for(const f in c){const d=c[f];l[f]!==void 0&&Ne(d)&&(d.set(l[f],!1),yo(u)&&u.add(f))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,lf.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),zh.current||yx(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ml.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){lf.delete(this.current),this.projection&&this.projection.unmount(),vt(this.notifyUpdate),vt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=yn.has(t),i=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&$.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),o()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,i,o){let s,l;for(let a=0;a<vx;a++){const u=Bh[a],{isEnabled:c,Feature:f,ProjectionNode:d,MeasureLayout:g}=Gr[u];d&&(s=d),c(n)&&(!this.features[u]&&f&&(this.features[u]=new f(this)),g&&(l=g))}if((this.type==="html"||this.type==="svg")&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);const{layoutId:a,layout:u,drag:c,dragConstraints:f,layoutScroll:d,layoutRoot:g}=n;this.projection.setOptions({layoutId:a,layout:u,alwaysMeasureLayout:!!c||f&&An(f),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:o,layoutScroll:d,layoutRoot:g})}return l}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ee()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<af.length;r++){const i=af[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o=t["on"+i];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=gx(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<xx;r++){const i=La[r],o=this.props[i];(Wr(o)||o===!1)&&(n[i]=o)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Yn(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=Fa(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!Ne(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Ka),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class bh extends wx{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},o){let s=Fv(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),s&&(s=i(s))),o){_v(this,r,s);const l=mx(this,r,s,n);n=l.transitionEnd,r=l.target}return{transition:t,transitionEnd:n,...r}}}function Sx(e){return window.getComputedStyle(e)}class kx extends bh{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(yn.has(n)){const r=Ua(n);return r&&r.default||0}else{const r=Sx(t),i=(Vp(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Nh(t,n)}build(t,n,r,i){Aa(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,n){return Ia(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Ne(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,i){_p(t,n,r,i)}}class Px extends bh{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(yn.has(n)){const r=Ua(n);return r&&r.default||0}return n=Ip.has(n)?n:Na(n),t.getAttribute(n)}measureInstanceViewportBox(){return ee()}scrapeMotionValuesFromProps(t,n){return Op(t,n)}build(t,n,r,i){Ra(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){Fp(t,n,r,i)}mount(t){this.isSVGTag=_a(t.tagName),super.mount(t)}}const Cx=(e,t)=>Ma(e)?new Px(t,{enableHardwareAcceleration:!1}):new kx(t,{enableHardwareAcceleration:!0}),Tx={layout:{ProjectionNode:Fh,MeasureLayout:Lh}},Ex={...qv,...vg,...rx,...Tx},V=Ey((e,t)=>og(e,t,Ex,Cx)),Qa=({size:e="large",className:t=""})=>{const n={small:"w-8 h-8",medium:"w-12 h-12",large:"w-20 h-20",xl:"w-32 h-32"};return y.jsxs(V.div,{className:`relative ${n[e]} ${t}`,initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.8},children:[y.jsx(V.div,{className:"absolute inset-0 rounded-full bg-space-blue/20 blur-md",animate:{scale:[1,1.1,1]},transition:{duration:3,repeat:1/0}}),y.jsx(V.div,{className:"absolute inset-0 rounded-full bg-gradient-to-br from-space-blue to-space-purple border-2 border-space-blue/50",animate:{rotate:360},transition:{duration:20,repeat:1/0,ease:"linear"}}),y.jsx(V.div,{className:"absolute top-1/4 left-1/4 w-1/2 h-1/2 rounded-full bg-space-dark/80",animate:{x:[0,10,0],y:[0,5,0]},transition:{duration:4,repeat:1/0}}),y.jsx(V.div,{className:"absolute top-1/2 left-1/2 w-2 h-2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-star-white",animate:{opacity:[.5,1,.5]},transition:{duration:2,repeat:1/0}}),[...Array(3)].map((r,i)=>y.jsx(V.div,{className:"absolute w-1 h-1 bg-star-white rounded-full",style:{top:"50%",left:"50%"},animate:{rotate:360,x:[0,30,0,-30,0],y:[0,-15,0,15,0]},transition:{duration:6+i*2,repeat:1/0,ease:"linear",delay:i*.5}},i))]})},jx=()=>{const[e,t]=R.useState(!1),n=[{href:"#home",label:"Home"},{href:"#services",label:"Services"},{href:"#about",label:"About"},{href:"#contact",label:"Contact"}];return y.jsx("nav",{className:"fixed top-0 left-0 right-0 z-50 bg-space-dark/95 backdrop-blur-md border-b border-space-blue/20",style:{background:"linear-gradient(135deg, rgba(10, 15, 44, 0.95) 0%, rgba(30, 27, 75, 0.9) 100%)"},children:y.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[y.jsxs("div",{className:"flex justify-between items-center h-16",children:[y.jsxs(V.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},className:"flex-shrink-0 flex items-center space-x-3",children:[y.jsx(Qa,{size:"small"}),y.jsx("a",{href:"#home",className:"text-xl font-orbitron font-bold bg-gradient-to-r from-space-blue to-space-purple bg-clip-text text-transparent",children:"Eclipse Softworks"})]}),y.jsx("div",{className:"hidden md:block",children:y.jsx("div",{className:"ml-10 flex items-baseline space-x-8",children:n.map((r,i)=>y.jsxs(V.a,{href:r.href,initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5,delay:i*.1},className:"text-star-silver hover:text-space-blue px-3 py-2 text-sm font-orbitron font-medium transition-all duration-300 relative group",children:[r.label,y.jsx("span",{className:"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-space-blue to-space-purple transition-all duration-300 group-hover:w-full"}),y.jsx("span",{className:"absolute inset-0 bg-space-blue/10 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]},r.href))})}),y.jsx("div",{className:"md:hidden",children:y.jsx("button",{onClick:()=>t(!e),className:"text-star-silver hover:text-space-blue focus:outline-none focus:text-space-blue transition-colors duration-300","aria-label":"Toggle menu",children:y.jsx("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e?y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),e&&y.jsx(V.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"md:hidden",children:y.jsx("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-space-dark/80 backdrop-blur-md rounded-lg mt-2 border border-space-blue/20",children:n.map(r=>y.jsx("a",{href:r.href,className:"text-star-silver hover:text-space-blue block px-3 py-2 text-base font-orbitron font-medium transition-all duration-300 rounded hover:bg-space-blue/10",onClick:()=>t(!1),children:r.label},r.href))})})]})})},Nx=()=>y.jsx("section",{id:"home",className:"min-h-screen flex items-center justify-center pt-16 px-4 sm:px-6 lg:px-8 relative",children:y.jsxs("div",{className:"max-w-7xl mx-auto text-center",children:[y.jsxs(V.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8},className:"space-y-12",children:[y.jsx(V.div,{initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},transition:{duration:1,delay:.2},className:"flex justify-center mb-8",children:y.jsx(Qa,{size:"xl",className:"animate-float"})}),y.jsxs(V.h1,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.4},className:"text-6xl sm:text-7xl lg:text-9xl font-orbitron font-bold text-star-white leading-tight tracking-wider",children:[y.jsx("span",{className:"block bg-gradient-to-r from-space-blue via-space-purple to-space-cyan bg-clip-text text-transparent",children:"Eclipse"}),y.jsx("span",{className:"block text-star-white",children:"Softworks"})]}),y.jsx(V.h2,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.6},className:"text-2xl sm:text-3xl lg:text-4xl font-orbitron font-medium text-space-blue tracking-wide",children:"Innovating with Purpose"}),y.jsx(V.p,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:.8},className:"text-lg sm:text-xl text-star-silver max-w-4xl mx-auto leading-relaxed font-inter",children:"Explore the cosmos of technology with our Luna/space-themed software and AI solutions. From South Africa to the stars, we craft digital experiences that transcend boundaries."}),y.jsxs(V.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,delay:1},className:"pt-8 flex flex-col sm:flex-row gap-4 justify-center items-center",children:[y.jsx(V.a,{href:"#services",className:"px-8 py-4 bg-gradient-to-r from-space-blue to-space-purple text-star-white font-orbitron font-medium rounded-full border border-space-blue/50 hover:shadow-lg hover:shadow-space-blue/25 transition-all duration-300 animate-pulse-glow",whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":"Explore our services",children:"Explore Services"}),y.jsx(V.a,{href:"#contact",className:"px-8 py-4 bg-transparent text-space-blue font-orbitron font-medium rounded-full border-2 border-space-blue hover:bg-space-blue hover:text-star-white transition-all duration-300",whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":"Get in touch with Eclipse Softworks",children:"Get in Touch"})]}),y.jsx(V.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:1,delay:1.2},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:y.jsx(V.div,{animate:{y:[0,10,0]},transition:{duration:2,repeat:1/0},className:"text-space-blue",children:y.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})})})]}),y.jsxs("div",{className:"absolute inset-0 -z-10 overflow-hidden",children:[y.jsx(V.div,{className:"absolute top-1/4 left-1/4 w-96 h-96 bg-space-purple/20 rounded-full blur-3xl",animate:{scale:[1,1.2,1],opacity:[.2,.4,.2]},transition:{duration:8,repeat:1/0}}),y.jsx(V.div,{className:"absolute bottom-1/4 right-1/4 w-80 h-80 bg-space-cyan/15 rounded-full blur-3xl",animate:{scale:[1.2,1,1.2],opacity:[.15,.3,.15]},transition:{duration:10,repeat:1/0}}),y.jsx(V.div,{className:"absolute top-1/3 right-1/5 w-4 h-4 bg-nebula-pink rounded-full",animate:{y:[0,-20,0],x:[0,10,0]},transition:{duration:6,repeat:1/0}}),y.jsx(V.div,{className:"absolute bottom-1/3 left-1/5 w-3 h-3 bg-space-cyan rounded-full",animate:{y:[0,15,0],x:[0,-8,0]},transition:{duration:8,repeat:1/0,delay:2}})]})]})}),Vx=()=>{const e=[{title:"AI Solutions",description:"Advanced artificial intelligence and machine learning systems that navigate the cosmos of data to unlock stellar insights.",icon:y.jsx("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})}),gradient:"from-space-blue to-space-cyan"},{title:"Web & App Development",description:"Cosmic web experiences and stellar mobile applications that transcend digital boundaries with cutting-edge technology.",icon:y.jsx("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"})}),gradient:"from-space-purple to-nebula-purple"},{title:"Cybersecurity",description:"Galactic-grade security solutions that protect your digital universe from cosmic threats and space-age vulnerabilities.",icon:y.jsx("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})}),gradient:"from-nebula-pink to-space-purple"},{title:"Automation Tools",description:"Orbital automation systems that streamline workflows across the digital galaxy, reducing manual tasks to stardust.",icon:y.jsxs("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),gradient:"from-space-cyan to-space-indigo"},{title:"Custom Systems",description:"Bespoke enterprise constellations and digital ecosystems engineered for your unique cosmic business requirements.",icon:y.jsx("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),gradient:"from-space-indigo to-space-blue"},{title:"Cloud Solutions",description:"Nebula-powered cloud infrastructure that scales across the digital cosmos, providing stellar performance and reliability.",icon:y.jsx("svg",{className:"w-10 h-10",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"})}),gradient:"from-nebula-purple to-nebula-pink"}];return y.jsxs("section",{id:"services",className:"py-20 px-4 sm:px-6 lg:px-8 relative",children:[y.jsxs("div",{className:"absolute inset-0 overflow-hidden",children:[y.jsx("div",{className:"absolute top-1/4 left-1/6 w-64 h-64 bg-space-purple/10 rounded-full blur-3xl animate-pulse"}),y.jsx("div",{className:"absolute bottom-1/3 right-1/6 w-80 h-80 bg-space-cyan/10 rounded-full blur-3xl animate-pulse",style:{animationDelay:"2s"}})]}),y.jsxs("div",{className:"max-w-7xl mx-auto relative z-10",children:[y.jsxs(V.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-20",children:[y.jsxs(V.h2,{className:"text-4xl sm:text-5xl lg:text-6xl font-orbitron font-bold text-star-white mb-6",initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},transition:{duration:1,delay:.2},viewport:{once:!0},children:["Our ",y.jsx("span",{className:"bg-gradient-to-r from-space-blue via-space-purple to-space-cyan bg-clip-text text-transparent glow-text",children:"Services"})]}),y.jsx(V.p,{className:"text-xl text-star-silver max-w-4xl mx-auto font-inter leading-relaxed",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},children:"Navigate the cosmos of technology with our stellar solutions. From AI constellations to cybersecurity shields, we craft digital experiences that transcend earthly boundaries."})]}),y.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map((t,n)=>y.jsxs(V.div,{initial:{opacity:0,y:50,rotateX:-15},whileInView:{opacity:1,y:0,rotateX:0},transition:{duration:.8,delay:n*.15,type:"spring",stiffness:100},viewport:{once:!0},whileHover:{y:-10,rotateX:5,transition:{duration:.3}},className:"space-card group cursor-pointer",children:[y.jsxs(V.div,{className:`w-16 h-16 rounded-full bg-gradient-to-br ${t.gradient} p-4 mb-6 group-hover:scale-110 transition-all duration-300 relative`,whileHover:{rotate:360},transition:{duration:.6},children:[y.jsx("div",{className:"text-star-white w-full h-full flex items-center justify-center",children:t.icon}),y.jsx("div",{className:"absolute inset-0 rounded-full border-2 border-space-blue/30 animate-pulse"})]}),y.jsx("h3",{className:"text-2xl font-orbitron font-bold text-star-white mb-4 group-hover:text-space-blue transition-colors duration-300",children:t.title}),y.jsx("p",{className:"text-star-silver leading-relaxed font-inter group-hover:text-star-white transition-colors duration-300",children:t.description}),y.jsx("div",{className:"absolute top-4 right-4 w-2 h-2 bg-space-blue rounded-full animate-twinkle"}),y.jsx("div",{className:"absolute bottom-6 left-6 w-1 h-1 bg-space-cyan rounded-full animate-twinkle",style:{animationDelay:"1s"}})]},t.title))})]})]})},Lx=()=>{const e=[{number:"100+",label:"Stellar Projects",icon:"🚀"},{number:"24/7",label:"Cosmic Support",icon:"🌌"},{number:"5★",label:"Galaxy Rating",icon:"⭐"},{number:"∞",label:"Innovation Potential",icon:"🌟"}];return y.jsxs("section",{id:"about",className:"py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden",children:[y.jsxs("div",{className:"absolute inset-0",children:[y.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-space-dark/90 via-space-dark/70 to-space-dark/90"}),y.jsx("div",{className:"absolute top-1/3 left-1/4 w-96 h-96 bg-space-purple/20 rounded-full blur-3xl animate-pulse"}),y.jsx("div",{className:"absolute bottom-1/4 right-1/3 w-80 h-80 bg-space-cyan/15 rounded-full blur-3xl animate-pulse",style:{animationDelay:"3s"}}),[...Array(20)].map((t,n)=>y.jsx("div",{className:"absolute w-1 h-1 bg-star-white rounded-full animate-twinkle",style:{left:`${Math.random()*100}%`,top:`${Math.random()*100}%`,animationDelay:`${Math.random()*5}s`}},n))]}),y.jsx("div",{className:"max-w-6xl mx-auto relative z-10",children:y.jsxs(V.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"space-y-16",children:[y.jsx("div",{className:"text-center",children:y.jsxs(V.h2,{className:"text-4xl sm:text-5xl lg:text-6xl font-orbitron font-bold text-star-white mb-6",initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},transition:{duration:1,delay:.2},viewport:{once:!0},children:["About ",y.jsx("span",{className:"bg-gradient-to-r from-space-blue via-space-purple to-space-cyan bg-clip-text text-transparent glow-text",children:"Eclipse Softworks"})]})}),y.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center",children:[y.jsxs("div",{className:"space-y-8",children:[y.jsxs(V.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.3},viewport:{once:!0},className:"space-card",children:[y.jsx("h3",{className:"text-2xl font-orbitron font-bold text-space-blue mb-4",children:"Our Cosmic Mission"}),y.jsx("p",{className:"text-lg text-star-silver leading-relaxed font-inter",children:"Eclipse Softworks (Pty) Ltd is a South African software and AI innovation company dedicated to building next-generation tools and digital systems. We navigate the cosmos of technology to deliver solutions that transform businesses and drive meaningful impact across Africa and beyond."})]}),y.jsxs(V.div,{initial:{opacity:0,x:-50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.5},viewport:{once:!0},className:"space-card",children:[y.jsx("h3",{className:"text-2xl font-orbitron font-bold text-space-purple mb-4",children:"Innovating with Purpose"}),y.jsxs("p",{className:"text-lg text-star-silver leading-relaxed font-inter",children:["Our mission transcends earthly boundaries: ",y.jsx("strong",{className:"text-space-blue glow-text",children:"Innovating with Purpose"}),". We believe technology should solve real problems and create stellar opportunities. From AI-powered automation to custom enterprise constellations, we craft solutions that are not just technically excellent, but strategically aligned with your cosmic goals."]})]})]}),y.jsx(V.div,{initial:{opacity:0,x:50},whileInView:{opacity:1,x:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"relative",children:y.jsxs("div",{className:"relative w-full h-96 rounded-2xl overflow-hidden",children:[y.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-space-blue/30 via-space-purple/20 to-space-cyan/30 backdrop-blur-sm"}),[...Array(3)].map((t,n)=>y.jsx(V.div,{className:"absolute border border-space-blue/30 rounded-full",style:{width:`${(n+1)*80}px`,height:`${(n+1)*80}px`,top:"50%",left:"50%",transform:"translate(-50%, -50%)"},animate:{rotate:360},transition:{duration:10+n*5,repeat:1/0,ease:"linear"}},n)),y.jsx("div",{className:"absolute top-1/2 left-1/2 w-8 h-8 -translate-x-1/2 -translate-y-1/2 bg-gradient-to-r from-space-blue to-space-purple rounded-full animate-pulse-glow"}),[...Array(8)].map((t,n)=>y.jsx(V.div,{className:"absolute w-2 h-2 bg-star-white rounded-full",style:{left:`${20+n*10}%`,top:`${30+n*5}%`},animate:{y:[0,-20,0],opacity:[.3,1,.3]},transition:{duration:3+n*.5,repeat:1/0,delay:n*.2}},n))]})})]}),y.jsx(V.div,{initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.7},viewport:{once:!0},className:"grid grid-cols-2 md:grid-cols-4 gap-6",children:e.map((t,n)=>y.jsxs(V.div,{initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.6,delay:.8+n*.1},viewport:{once:!0},whileHover:{scale:1.05,y:-5},className:"text-center space-card",children:[y.jsx("div",{className:"text-4xl mb-2",children:t.icon}),y.jsx("div",{className:"text-3xl font-orbitron font-bold bg-gradient-to-r from-space-blue to-space-purple bg-clip-text text-transparent mb-2",children:t.number}),y.jsx("div",{className:"text-star-silver font-inter text-sm",children:t.label})]},t.label))})]})})]})},Mx=()=>{const[e,t]=R.useState({name:"",email:"",message:""}),[n,r]=R.useState(!1),[i,o]=R.useState(null),s=a=>{t({...e,[a.target.name]:a.target.value})},l=async a=>{a.preventDefault(),r(!0);try{const u=a.target,c=new FormData(u);(await fetch("https://formsubmit.co/<EMAIL>",{method:"POST",body:c})).ok?(o("success"),t({name:"",email:"",message:""})):o("error")}catch{o("error")}r(!1)};return y.jsxs("section",{id:"contact",className:"py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden",children:[y.jsxs("div",{className:"absolute inset-0",children:[y.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-space-dark/95 via-space-dark/80 to-space-dark/95"}),y.jsx("div",{className:"absolute top-1/4 right-1/4 w-96 h-96 bg-space-blue/10 rounded-full blur-3xl animate-pulse"}),y.jsx("div",{className:"absolute bottom-1/3 left-1/4 w-80 h-80 bg-space-purple/10 rounded-full blur-3xl animate-pulse",style:{animationDelay:"2s"}}),[...Array(15)].map((a,u)=>y.jsx(V.div,{className:"absolute w-1 h-1 bg-space-cyan rounded-full",style:{left:`${Math.random()*100}%`,top:`${Math.random()*100}%`},animate:{opacity:[.3,1,.3],scale:[1,1.5,1]},transition:{duration:2+Math.random()*2,repeat:1/0,delay:Math.random()*2}},u))]}),y.jsxs("div",{className:"max-w-6xl mx-auto relative z-10",children:[y.jsxs(V.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"text-center mb-20",children:[y.jsxs(V.h2,{className:"text-4xl sm:text-5xl lg:text-6xl font-orbitron font-bold text-star-white mb-6",initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},transition:{duration:1,delay:.2},viewport:{once:!0},children:["Initiate ",y.jsx("span",{className:"bg-gradient-to-r from-space-blue via-space-purple to-space-cyan bg-clip-text text-transparent glow-text",children:"Contact"})]}),y.jsx(V.p,{className:"text-xl text-star-silver max-w-3xl mx-auto font-inter leading-relaxed mb-8",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},children:"Ready to launch your next cosmic project? Establish communication with our stellar team and let's navigate the digital universe together to bring your visionary ideas to life."}),y.jsxs(V.div,{className:"flex flex-col sm:flex-row gap-6 justify-center items-center",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.6},viewport:{once:!0},children:[y.jsxs(V.a,{href:"mailto:<EMAIL>",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-space-blue/20 to-space-purple/20 border border-space-blue/50 rounded-full text-space-blue hover:text-star-white hover:bg-gradient-to-r hover:from-space-blue hover:to-space-purple transition-all duration-300 backdrop-blur-sm",whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":"Send email to Eclipse Softworks",children:[y.jsx("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),"<EMAIL>"]}),y.jsxs(V.a,{href:"tel:+27123456789",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-space-cyan/20 to-space-indigo/20 border border-space-cyan/50 rounded-full text-space-cyan hover:text-star-white hover:bg-gradient-to-r hover:from-space-cyan hover:to-space-indigo transition-all duration-300 backdrop-blur-sm",whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":"Call Eclipse Softworks",children:[y.jsx("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),"+27 (0) 12 345 6789"]})]})]}),y.jsxs(V.div,{initial:{opacity:0,y:50},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.8},viewport:{once:!0},className:"space-card max-w-3xl mx-auto",children:[y.jsxs("div",{className:"text-center mb-8",children:[y.jsx("h3",{className:"text-2xl font-orbitron font-bold text-space-blue mb-2",children:"Transmission Form"}),y.jsx("p",{className:"text-star-silver font-inter",children:"Send us a message through the cosmic network"})]}),y.jsxs("form",{onSubmit:l,className:"space-y-8",children:[y.jsxs(V.div,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:1},viewport:{once:!0},children:[y.jsx("label",{htmlFor:"name",className:"block text-sm font-orbitron font-medium text-space-blue mb-3",children:"Cosmic Identity *"}),y.jsx("input",{type:"text",id:"name",name:"name",value:e.name,onChange:s,required:!0,className:"w-full px-6 py-4 bg-space-dark/60 border-2 border-space-blue/30 rounded-xl text-star-white placeholder-star-silver/60 focus:outline-none focus:ring-2 focus:ring-space-blue focus:border-space-blue transition-all duration-300 backdrop-blur-sm font-inter",placeholder:"Your stellar designation"})]}),y.jsxs(V.div,{initial:{opacity:0,x:20},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:1.2},viewport:{once:!0},children:[y.jsx("label",{htmlFor:"email",className:"block text-sm font-orbitron font-medium text-space-purple mb-3",children:"Galactic Communication Channel *"}),y.jsx("input",{type:"email",id:"email",name:"email",value:e.email,onChange:s,required:!0,className:"w-full px-6 py-4 bg-space-dark/60 border-2 border-space-purple/30 rounded-xl text-star-white placeholder-star-silver/60 focus:outline-none focus:ring-2 focus:ring-space-purple focus:border-space-purple transition-all duration-300 backdrop-blur-sm font-inter",placeholder:"<EMAIL>"})]}),y.jsxs(V.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.6,delay:1.4},viewport:{once:!0},children:[y.jsx("label",{htmlFor:"message",className:"block text-sm font-orbitron font-medium text-space-cyan mb-3",children:"Cosmic Transmission *"}),y.jsx("textarea",{id:"message",name:"message",value:e.message,onChange:s,required:!0,rows:6,className:"w-full px-6 py-4 bg-space-dark/60 border-2 border-space-cyan/30 rounded-xl text-star-white placeholder-star-silver/60 focus:outline-none focus:ring-2 focus:ring-space-cyan focus:border-space-cyan transition-all duration-300 resize-vertical backdrop-blur-sm font-inter",placeholder:"Describe your stellar project vision..."})]}),y.jsx(V.div,{className:"text-center pt-4",initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},transition:{duration:.6,delay:1.6},viewport:{once:!0},children:y.jsx(V.button,{type:"submit",disabled:n,className:"px-12 py-4 bg-gradient-to-r from-space-blue via-space-purple to-space-cyan text-star-white font-orbitron font-bold rounded-full border-2 border-space-blue/50 hover:shadow-2xl hover:shadow-space-blue/30 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed animate-pulse-glow",whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":"Send cosmic message",children:n?y.jsxs("span",{className:"flex items-center",children:[y.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[y.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),y.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Transmitting..."]}):"Launch Transmission"})}),i==="success"&&y.jsxs(V.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-center p-4 bg-green-500/20 border border-green-500/50 rounded-xl backdrop-blur-sm",children:[y.jsx("div",{className:"text-green-400 font-orbitron font-medium mb-2",children:"🚀 Transmission Successful!"}),y.jsx("div",{className:"text-star-silver font-inter text-sm",children:"Your cosmic message has been received. We'll respond within 24 Earth hours."})]}),i==="error"&&y.jsxs(V.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-center p-4 bg-red-500/20 border border-red-500/50 rounded-xl backdrop-blur-sm",children:[y.jsx("div",{className:"text-red-400 font-orbitron font-medium mb-2",children:"⚠️ Transmission Failed"}),y.jsx("div",{className:"text-star-silver font-inter text-sm",children:"Communication error detected. Please try again or contact us directly."})]})]})]})]})]})},Ax=()=>{const e=new Date().getFullYear(),t=[{href:"#home",label:"Home",icon:"🏠"},{href:"#services",label:"Services",icon:"🚀"},{href:"#about",label:"About",icon:"🌌"},{href:"#contact",label:"Contact",icon:"📡"}],n=[{href:"https://linkedin.com/company/eclipse-softworks",label:"LinkedIn",icon:y.jsx("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})}),gradient:"from-blue-500 to-blue-600"},{href:"https://github.com/eclipse-softworks",label:"GitHub",icon:y.jsx("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})}),gradient:"from-gray-700 to-gray-900"},{href:"https://twitter.com/eclipse_softworks",label:"X (Twitter)",icon:y.jsx("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{d:"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"})}),gradient:"from-gray-800 to-black"},{href:"mailto:<EMAIL>",label:"Email",icon:y.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),gradient:"from-space-blue to-space-cyan"}];return y.jsxs("footer",{className:"relative overflow-hidden bg-gradient-to-br from-space-dark via-space-dark/95 to-space-dark border-t border-space-blue/30 py-16 px-4 sm:px-6 lg:px-8",children:[y.jsxs("div",{className:"absolute inset-0",children:[y.jsx("div",{className:"absolute bottom-0 left-1/4 w-96 h-96 bg-space-purple/10 rounded-full blur-3xl"}),y.jsx("div",{className:"absolute top-0 right-1/3 w-80 h-80 bg-space-cyan/10 rounded-full blur-3xl"}),[...Array(30)].map((r,i)=>y.jsx("div",{className:"absolute w-1 h-1 bg-star-white rounded-full animate-twinkle",style:{left:`${Math.random()*100}%`,top:`${Math.random()*100}%`,animationDelay:`${Math.random()*5}s`}},i))]}),y.jsxs("div",{className:"max-w-7xl mx-auto relative z-10",children:[y.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12",children:[y.jsxs(V.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8},viewport:{once:!0},className:"lg:col-span-2 space-y-6",children:[y.jsxs("div",{className:"flex items-center space-x-4",children:[y.jsx(Qa,{size:"medium"}),y.jsxs("div",{children:[y.jsx("h3",{className:"text-2xl font-orbitron font-bold bg-gradient-to-r from-space-blue to-space-purple bg-clip-text text-transparent",children:"Eclipse Softworks"}),y.jsx("p",{className:"text-star-silver font-inter text-sm",children:"(Pty) Ltd"})]})]}),y.jsx("p",{className:"text-star-silver leading-relaxed font-inter max-w-md",children:"Navigating the digital cosmos to deliver innovative software and AI solutions. We transform visionary ideas into stellar realities that transcend earthly boundaries."}),y.jsxs("div",{className:"flex items-center space-x-2 text-space-cyan",children:[y.jsxs("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),y.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),y.jsx("span",{className:"text-sm font-inter",children:"South Africa & Beyond"})]})]}),y.jsxs(V.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.2},viewport:{once:!0},className:"space-y-6",children:[y.jsx("h4",{className:"text-lg font-orbitron font-bold text-space-blue",children:"Navigation"}),y.jsx("ul",{className:"space-y-3",children:t.map((r,i)=>y.jsx(V.li,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.6,delay:.3+i*.1},viewport:{once:!0},children:y.jsxs("a",{href:r.href,className:"flex items-center space-x-2 text-star-silver hover:text-space-cyan transition-all duration-300 group font-inter",children:[y.jsx("span",{className:"text-sm group-hover:scale-110 transition-transform duration-300",children:r.icon}),y.jsx("span",{className:"group-hover:translate-x-1 transition-transform duration-300",children:r.label})]})},r.href))})]}),y.jsxs(V.div,{initial:{opacity:0,y:30},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.4},viewport:{once:!0},className:"space-y-6",children:[y.jsx("h4",{className:"text-lg font-orbitron font-bold text-space-purple",children:"Connect"}),y.jsx("div",{className:"grid grid-cols-2 gap-3",children:n.map((r,i)=>y.jsx(V.a,{href:r.href,initial:{opacity:0,scale:.8},whileInView:{opacity:1,scale:1},transition:{duration:.6,delay:.5+i*.1},viewport:{once:!0},whileHover:{scale:1.1,y:-2},className:`flex items-center justify-center p-3 bg-gradient-to-br ${r.gradient} rounded-xl text-star-white hover:shadow-lg hover:shadow-space-blue/20 transition-all duration-300 group`,"aria-label":r.label,children:y.jsx("div",{className:"group-hover:rotate-12 transition-transform duration-300",children:r.icon})},r.label))})]})]}),y.jsx(V.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.8,delay:.6},viewport:{once:!0},className:"border-t border-space-blue/20 mt-12 pt-8",children:y.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[y.jsxs("p",{className:"text-star-silver font-inter text-sm",children:["© ",e," Eclipse Softworks (Pty) Ltd. All rights reserved."]}),y.jsxs("div",{className:"flex items-center space-x-6 text-sm",children:[y.jsx("a",{href:"#privacy",className:"text-star-silver hover:text-space-cyan transition-colors duration-300 font-inter",children:"Privacy Policy"}),y.jsx("a",{href:"#terms",className:"text-star-silver hover:text-space-cyan transition-colors duration-300 font-inter",children:"Terms of Service"}),y.jsx("span",{className:"text-space-blue font-orbitron text-xs",children:"Made with 🚀 in South Africa"})]})]})})]})]})};function Dx(){return y.jsxs("div",{className:"min-h-screen bg-space-dark text-star-white relative overflow-hidden",children:[y.jsxs("div",{className:"fixed inset-0 z-0",children:[y.jsx("div",{className:"absolute inset-0 bg-space-gradient"}),y.jsx("div",{className:"absolute inset-0 bg-nebula-gradient opacity-30"}),[...Array(50)].map((e,t)=>y.jsx("div",{className:"absolute w-1 h-1 bg-star-white rounded-full animate-twinkle",style:{left:`${Math.random()*100}%`,top:`${Math.random()*100}%`,animationDelay:`${Math.random()*3}s`}},t))]}),y.jsxs("div",{className:"relative z-10",children:[y.jsx(jx,{}),y.jsxs("main",{children:[y.jsx(Nx,{}),y.jsx(Vx,{}),y.jsx(Lx,{}),y.jsx(Mx,{})]}),y.jsx(Ax,{})]})]})}Es.createRoot(document.getElementById("root")).render(y.jsx(Il.StrictMode,{children:y.jsx(Dx,{})}));
