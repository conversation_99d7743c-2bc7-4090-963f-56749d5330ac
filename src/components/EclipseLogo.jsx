import { motion } from 'framer-motion'

const EclipseLogo = ({ size = 'large', className = '' }) => {
  const sizeClasses = {
    small: 'w-8 h-8',
    medium: 'w-12 h-12',
    large: 'w-20 h-20',
    xl: 'w-32 h-32'
  }

  return (
    <motion.div
      className={`relative ${sizeClasses[size]} ${className}`}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.8 }}
    >
      {/* Outer glow ring */}
      <motion.div
        className="absolute inset-0 rounded-full bg-space-blue/20 blur-md"
        animate={{ scale: [1, 1.1, 1] }}
        transition={{ duration: 3, repeat: Infinity }}
      />
      
      {/* Main eclipse circle */}
      <motion.div
        className="absolute inset-0 rounded-full bg-gradient-to-br from-space-blue to-space-purple border-2 border-space-blue/50"
        animate={{ rotate: 360 }}
        transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
      />
      
      {/* Eclipse shadow */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-1/2 h-1/2 rounded-full bg-space-dark/80"
        animate={{ 
          x: [0, 10, 0],
          y: [0, 5, 0]
        }}
        transition={{ duration: 4, repeat: Infinity }}
      />
      
      {/* Inner light core */}
      <motion.div
        className="absolute top-1/2 left-1/2 w-2 h-2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-star-white"
        animate={{ opacity: [0.5, 1, 0.5] }}
        transition={{ duration: 2, repeat: Infinity }}
      />
      
      {/* Orbiting stars */}
      {[...Array(3)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute w-1 h-1 bg-star-white rounded-full"
          style={{
            top: '50%',
            left: '50%',
          }}
          animate={{
            rotate: 360,
            x: [0, 30, 0, -30, 0],
            y: [0, -15, 0, 15, 0],
          }}
          transition={{
            duration: 6 + i * 2,
            repeat: Infinity,
            ease: "linear",
            delay: i * 0.5
          }}
        />
      ))}
    </motion.div>
  )
}

export default EclipseLogo
