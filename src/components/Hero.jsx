import { motion } from 'framer-motion'
import <PERSON><PERSON><PERSON> from './EclipseLogo'

const Hero = () => {
  return (
    <section id="home" className="min-h-screen flex items-center justify-center pt-16 px-4 sm:px-6 lg:px-8 relative">
      <div className="max-w-7xl mx-auto text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-12"
        >
          {/* Eclipse Logo */}
          <motion.div
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1, delay: 0.2 }}
            className="flex justify-center mb-8"
          >
            <EclipseLogo size="xl" className="animate-float" />
          </motion.div>
          {/* Company Name */}
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-6xl sm:text-7xl lg:text-9xl font-orbitron font-bold text-star-white leading-tight tracking-wider"
          >
            <span className="block bg-gradient-to-r from-space-blue via-space-purple to-space-cyan bg-clip-text text-transparent">
              Eclipse
            </span>
            <span className="block text-star-white">Softworks</span>
          </motion.h1>

          {/* Tagline */}
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-2xl sm:text-3xl lg:text-4xl font-orbitron font-medium text-space-blue tracking-wide"
          >
            Innovating with Purpose
          </motion.h2>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-lg sm:text-xl text-star-silver max-w-4xl mx-auto leading-relaxed font-inter"
          >
            Explore the cosmos of technology with our Luna/space-themed software and AI solutions.
            From South Africa to the stars, we craft digital experiences that transcend boundaries.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            className="pt-8 flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <motion.a
              href="#services"
              className="px-8 py-4 bg-gradient-to-r from-space-blue to-space-purple text-star-white font-orbitron font-medium rounded-full border border-space-blue/50 hover:shadow-lg hover:shadow-space-blue/25 transition-all duration-300 animate-pulse-glow"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              aria-label="Explore our services"
            >
              Explore Services
            </motion.a>
            <motion.a
              href="#contact"
              className="px-8 py-4 bg-transparent text-space-blue font-orbitron font-medium rounded-full border-2 border-space-blue hover:bg-space-blue hover:text-star-white transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              aria-label="Get in touch with Eclipse Softworks"
            >
              Get in Touch
            </motion.a>
          </motion.div>

          {/* Scroll Indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 1.2 }}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          >
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="text-space-blue"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Floating Cosmic Elements */}
        <div className="absolute inset-0 -z-10 overflow-hidden">
          {/* Nebula clouds */}
          <motion.div
            className="absolute top-1/4 left-1/4 w-96 h-96 bg-space-purple/20 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.2, 0.4, 0.2]
            }}
            transition={{ duration: 8, repeat: Infinity }}
          />
          <motion.div
            className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-space-cyan/15 rounded-full blur-3xl"
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.15, 0.3, 0.15]
            }}
            transition={{ duration: 10, repeat: Infinity }}
          />

          {/* Floating planets */}
          <motion.div
            className="absolute top-1/3 right-1/5 w-4 h-4 bg-nebula-pink rounded-full"
            animate={{
              y: [0, -20, 0],
              x: [0, 10, 0]
            }}
            transition={{ duration: 6, repeat: Infinity }}
          />
          <motion.div
            className="absolute bottom-1/3 left-1/5 w-3 h-3 bg-space-cyan rounded-full"
            animate={{
              y: [0, 15, 0],
              x: [0, -8, 0]
            }}
            transition={{ duration: 8, repeat: Infinity, delay: 2 }}
          />
        </div>
      </div>
    </section>
  )
}

export default Hero
