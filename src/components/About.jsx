import { motion } from 'framer-motion'

const About = () => {
  return (
    <section id="about" className="py-20 px-4 sm:px-6 lg:px-8 bg-slate-900/30">
      <div className="max-w-4xl mx-auto text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          {/* Section Header */}
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-8">
            About <span className="text-eclipse-blue">Eclipse Softworks</span>
          </h2>

          {/* About Content */}
          <div className="space-y-6 text-lg text-slate-300 leading-relaxed">
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              Eclipse Softworks (Pty) Ltd is a South African software and AI innovation company dedicated to building
              next-generation tools and digital systems. We combine cutting-edge technology with deep industry expertise
              to deliver solutions that transform businesses and drive meaningful impact across Africa and beyond.
            </motion.p>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              Our mission is simple: <strong className="text-eclipse-blue">Innovating with Purpose</strong>. We believe
              technology should solve real problems and create opportunities. From AI-powered automation to custom enterprise
              systems, we craft solutions that are not just technically excellent, but strategically aligned with your goals.
            </motion.p>
          </div>

          {/* Stats or Features */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16"
          >
            <div className="text-center">
              <div className="text-3xl font-bold text-eclipse-blue mb-2">100+</div>
              <div className="text-slate-300">Projects Delivered</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-eclipse-blue mb-2">24/7</div>
              <div className="text-slate-300">Support Available</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-eclipse-blue mb-2">5★</div>
              <div className="text-slate-300">Client Satisfaction</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default About
