import { motion } from 'framer-motion'

const About = () => {
  return (
    <section id="about" className="py-20 px-4 sm:px-6 lg:px-8 bg-slate-900/30">
      <div className="max-w-4xl mx-auto text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          {/* Section Header */}
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-8">
            About <span className="text-eclipse-blue">Eclipse Softworks</span>
          </h2>

          {/* About Content */}
          <div className="space-y-6 text-lg text-slate-300 leading-relaxed">
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              At Eclipse Softworks, we're passionate about harnessing the power of technology to solve complex problems 
              and create meaningful digital experiences. Our team of expert developers, AI specialists, and technical 
              consultants work collaboratively to deliver innovative solutions that drive real business value.
            </motion.p>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              We believe in the transformative potential of artificial intelligence and modern software development 
              practices. Whether you're a startup looking to build your first product or an enterprise seeking to 
              modernize your technology stack, we bring the expertise and dedication needed to turn your vision into reality.
            </motion.p>
          </div>

          {/* Stats or Features */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16"
          >
            <div className="text-center">
              <div className="text-3xl font-bold text-eclipse-blue mb-2">100+</div>
              <div className="text-slate-300">Projects Delivered</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-eclipse-blue mb-2">24/7</div>
              <div className="text-slate-300">Support Available</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-eclipse-blue mb-2">5★</div>
              <div className="text-slate-300">Client Satisfaction</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default About
