import { motion } from 'framer-motion'
import Icon from './Icon'

const About = () => {
  const stats = [
    { number: '100+', label: 'Stellar Projects', icon: 'rocket' },
    { number: '24/7', label: 'Cosmic Support', icon: 'galaxy' },
    { number: '5★', label: 'Galaxy Rating', icon: 'star' },
    { number: '∞', label: 'Innovation Potential', icon: 'sparkle' }
  ]

  return (
    <section id="about" className="py-20 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Cosmic background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-space-dark/90 via-space-dark/70 to-space-dark/90"></div>
        <div className="absolute top-1/3 left-1/4 w-96 h-96 bg-space-purple/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/3 w-80 h-80 bg-space-cyan/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '3s' }}></div>

        {/* Floating cosmic elements */}
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-star-white rounded-full animate-twinkle"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
            }}
          />
        ))}
      </div>

      <div className="max-w-6xl mx-auto relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="space-y-16"
        >
          {/* Section Header */}
          <div className="text-center">
            <motion.h2
              className="text-4xl sm:text-5xl lg:text-6xl font-orbitron font-bold text-star-white mb-6"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.2 }}
              viewport={{ once: true }}
            >
              About <span className="bg-gradient-to-r from-space-blue via-space-purple to-space-cyan bg-clip-text text-transparent glow-text">Eclipse Softworks</span>
            </motion.h2>
          </div>

          {/* About Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            {/* Left side - Content */}
            <div className="space-y-8">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
                className="space-card"
              >
                <h3 className="text-2xl font-orbitron font-bold text-space-blue mb-4">Our Cosmic Mission</h3>
                <p className="text-lg text-star-silver leading-relaxed font-inter">
                  Eclipse Softworks (Pty) Ltd is a South African software and AI innovation company dedicated to building
                  next-generation tools and digital systems. We navigate the cosmos of technology to deliver solutions that
                  transform businesses and drive meaningful impact across Africa and beyond.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                viewport={{ once: true }}
                className="space-card"
              >
                <h3 className="text-2xl font-orbitron font-bold text-space-purple mb-4">Innovating with Purpose</h3>
                <p className="text-lg text-star-silver leading-relaxed font-inter">
                  Our mission transcends earthly boundaries: <strong className="text-space-blue glow-text">Innovating with Purpose</strong>.
                  We believe technology should solve real problems and create stellar opportunities. From AI-powered automation to
                  custom enterprise constellations, we craft solutions that are not just technically excellent, but strategically
                  aligned with your cosmic goals.
                </p>
              </motion.div>
            </div>

            {/* Right side - Visual element */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="relative w-full h-96 rounded-2xl overflow-hidden">
                {/* Cosmic visualization */}
                <div className="absolute inset-0 bg-gradient-to-br from-space-blue/30 via-space-purple/20 to-space-cyan/30 backdrop-blur-sm"></div>

                {/* Orbital rings */}
                {[...Array(3)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute border border-space-blue/30 rounded-full"
                    style={{
                      width: `${(i + 1) * 80}px`,
                      height: `${(i + 1) * 80}px`,
                      top: '50%',
                      left: '50%',
                      transform: 'translate(-50%, -50%)',
                    }}
                    animate={{ rotate: 360 }}
                    transition={{
                      duration: 10 + i * 5,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                  />
                ))}

                {/* Central core */}
                <div className="absolute top-1/2 left-1/2 w-8 h-8 -translate-x-1/2 -translate-y-1/2 bg-gradient-to-r from-space-blue to-space-purple rounded-full animate-pulse-glow"></div>

                {/* Floating elements */}
                {[...Array(8)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-2 h-2 bg-star-white rounded-full"
                    style={{
                      left: `${20 + (i * 10)}%`,
                      top: `${30 + (i * 5)}%`,
                    }}
                    animate={{
                      y: [0, -20, 0],
                      opacity: [0.3, 1, 0.3],
                    }}
                    transition={{
                      duration: 3 + i * 0.5,
                      repeat: Infinity,
                      delay: i * 0.2,
                    }}
                  />
                ))}
              </div>
            </motion.div>
          </div>

          {/* Cosmic Stats */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            viewport={{ once: true }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="text-center space-card"
              >
                <div className="text-space-blue mb-2">
                  <Icon name={stat.icon} size="2xl" />
                </div>
                <div className="text-3xl font-orbitron font-bold bg-gradient-to-r from-space-blue to-space-purple bg-clip-text text-transparent mb-2">
                  {stat.number}
                </div>
                <div className="text-star-silver font-inter text-sm">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default About
