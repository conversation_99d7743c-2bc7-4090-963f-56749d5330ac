import React from 'react'

const Icon = ({ name, size = 'md', className = '', ...props }) => {
  const sizeClasses = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4', 
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-8 h-8',
    '2xl': 'w-10 h-10'
  }

  const iconSize = sizeClasses[size] || sizeClasses.md

  const icons = {
    rocket: (
      <svg className={`${iconSize} ${className}`} fill="currentColor" viewBox="0 0 24 24" {...props}>
        <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" />
        <path d="M12 16L8 20H16L12 16Z" />
        <circle cx="12" cy="8" r="2" />
      </svg>
    ),
    
    galaxy: (
      <svg className={`${iconSize} ${className}`} fill="currentColor" viewBox="0 0 24 24" {...props}>
        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L19 8L21 9ZM3 9L5 8L3 7V9ZM19 19H5C4.45 19 4 18.55 4 18S4.45 17 5 17H19C19.55 17 20 17.45 20 18S19.55 19 19 19ZM12 8.5C13.38 8.5 14.5 9.62 14.5 11S13.38 13.5 12 13.5S9.5 12.38 9.5 11S10.62 8.5 12 8.5ZM12 15C15.31 15 18 12.31 18 9C18 8.46 17.91 7.94 17.76 7.44L19.24 6.76C19.41 7.49 19.5 8.24 19.5 9C19.5 13.14 16.14 16.5 12 16.5S4.5 13.14 4.5 9C4.5 8.24 4.59 7.49 4.76 6.76L6.24 7.44C6.09 7.94 6 8.46 6 9C6 12.31 8.69 15 12 15Z" />
      </svg>
    ),

    star: (
      <svg className={`${iconSize} ${className}`} fill="currentColor" viewBox="0 0 24 24" {...props}>
        <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" />
      </svg>
    ),

    sparkle: (
      <svg className={`${iconSize} ${className}`} fill="currentColor" viewBox="0 0 24 24" {...props}>
        <path d="M12 1L14.5 7.5L21 10L14.5 12.5L12 19L9.5 12.5L3 10L9.5 7.5L12 1Z" />
        <path d="M19 3L20 5L22 6L20 7L19 9L18 7L16 6L18 5L19 3Z" />
        <path d="M19 15L20 17L22 18L20 19L19 21L18 19L16 18L18 17L19 15Z" />
      </svg>
    ),

    home: (
      <svg className={`${iconSize} ${className}`} fill="currentColor" viewBox="0 0 24 24" {...props}>
        <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z" />
      </svg>
    ),

    satellite: (
      <svg className={`${iconSize} ${className}`} fill="currentColor" viewBox="0 0 24 24" {...props}>
        <path d="M4 12L2 17L7 15L4 12ZM12 4L17 2L15 7L12 4ZM20.54 5.88L18.88 4.22C18.5 3.85 17.9 3.85 17.53 4.22L4.22 17.53C3.85 17.9 3.85 18.5 4.22 18.88L5.88 20.54C6.25 20.91 6.85 20.91 7.22 20.54L20.54 7.22C20.91 6.85 20.91 6.25 20.54 5.88ZM8.41 16.17L7.83 15.59L15.59 7.83L16.17 8.41L8.41 16.17Z" />
        <circle cx="8.5" cy="8.5" r="1.5" />
        <circle cx="15.5" cy="15.5" r="1.5" />
      </svg>
    ),

    services: (
      <svg className={`${iconSize} ${className}`} fill="currentColor" viewBox="0 0 24 24" {...props}>
        <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" />
        <path d="M5 16L6.5 19.5L10 21L6.5 22.5L5 26L3.5 22.5L0 21L3.5 19.5L5 16Z" />
        <path d="M19 16L20.5 19.5L24 21L20.5 22.5L19 26L17.5 22.5L14 21L17.5 19.5L19 16Z" />
      </svg>
    ),

    success: (
      <svg className={`${iconSize} ${className}`} fill="currentColor" viewBox="0 0 24 24" {...props}>
        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L19 8L21 9ZM3 9L5 8L3 7V9ZM12 8.5C13.38 8.5 14.5 9.62 14.5 11S13.38 13.5 12 13.5S9.5 12.38 9.5 11S10.62 8.5 12 8.5ZM12 15C15.31 15 18 12.31 18 9C18 8.46 17.91 7.94 17.76 7.44L19.24 6.76C19.41 7.49 19.5 8.24 19.5 9C19.5 13.14 16.14 16.5 12 16.5S4.5 13.14 4.5 9C4.5 8.24 4.59 7.49 4.76 6.76L6.24 7.44C6.09 7.94 6 8.46 6 9C6 12.31 8.69 15 12 15Z" />
        <path d="M9 16L11 18L15 14" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    ),

    heart: (
      <svg className={`${iconSize} ${className}`} fill="currentColor" viewBox="0 0 24 24" {...props}>
        <path d="M12 21.35L10.55 20.03C5.4 15.36 2 12.28 2 8.5C2 5.42 4.42 3 7.5 3C9.24 3 10.91 3.81 12 5.09C13.09 3.81 14.76 3 16.5 3C19.58 3 22 5.42 22 8.5C22 12.28 18.6 15.36 13.45 20.04L12 21.35Z" />
      </svg>
    )
  }

  const IconComponent = icons[name]
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found`)
    return null
  }

  return IconComponent
}

export default Icon
