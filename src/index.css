@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', system-ui, sans-serif;
    background: linear-gradient(135deg, #0A0F2C 0%, #1E1B4B 50%, #312E81 100%);
    background-attachment: fixed;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #0A0F2C;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #38BDF8, #8B5CF6);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #0EA5E9, #7C3AED);
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-space-blue to-space-purple hover:from-space-cyan hover:to-space-indigo text-star-white font-orbitron font-semibold py-3 px-8 rounded-full transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-space-blue focus:ring-opacity-50 shadow-lg hover:shadow-space-blue/25;
  }

  .btn-secondary {
    @apply border-2 border-space-blue text-space-blue hover:bg-space-blue hover:text-star-white font-orbitron font-semibold py-3 px-8 rounded-full transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-space-blue focus:ring-opacity-50 backdrop-blur-sm;
  }

  .card {
    @apply bg-space-dark/60 backdrop-blur-md border border-space-blue/30 rounded-xl p-6 hover:border-space-blue/60 transition-all duration-300 hover:transform hover:scale-105 hover:shadow-lg hover:shadow-space-blue/10;
  }

  .space-card {
    @apply bg-gradient-to-br from-space-dark/80 to-space-dark/60 backdrop-blur-md border border-space-blue/20 rounded-2xl p-8 hover:border-space-blue/50 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-space-blue/20 relative overflow-hidden;
  }

  .space-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(56, 189, 248, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .space-card:hover::before {
    opacity: 1;
  }

  .glow-text {
    text-shadow: 0 0 10px rgba(56, 189, 248, 0.5), 0 0 20px rgba(56, 189, 248, 0.3), 0 0 30px rgba(56, 189, 248, 0.1);
  }

  .nebula-bg {
    background: radial-gradient(ellipse at center, rgba(139, 92, 246, 0.2) 0%, rgba(59, 130, 246, 0.1) 50%, transparent 70%);
  }
}
