@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-eclipse-blue hover:bg-blue-400 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-eclipse-blue focus:ring-opacity-50;
  }
  
  .btn-secondary {
    @apply border-2 border-eclipse-blue text-eclipse-blue hover:bg-eclipse-blue hover:text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-eclipse-blue focus:ring-opacity-50;
  }
  
  .card {
    @apply bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-xl p-6 hover:border-eclipse-blue/50 transition-all duration-300 hover:transform hover:scale-105;
  }
}
