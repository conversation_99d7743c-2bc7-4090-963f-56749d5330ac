# Eclipse Softworks - Deployment Guide

## 🚀 Vercel Deployment Instructions

### Prerequisites
- Vercel account (sign up at [vercel.com](https://vercel.com))
- Git repository (GitHub, GitLab, or Bitbucket)
- Custom domain (optional)

### Method 1: Vercel CLI Deployment

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy from project directory**
   ```bash
   vercel --prod
   ```

4. **Follow the prompts:**
   - Set up and deploy? `Y`
   - Which scope? Select your account
   - Link to existing project? `N` (for first deployment)
   - Project name: `eclipse-softworks`
   - Directory: `./` (current directory)

### Method 2: Git Integration (Recommended)

1. **Push code to Git repository**
   ```bash
   git add .
   git commit -m "Complete Luna/space theme transformation"
   git push origin main
   ```

2. **Import project in Vercel Dashboard**
   - Go to [vercel.com/dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import your Git repository
   - Configure build settings:
     - Framework Preset: `Vite`
     - Build Command: `npm run build`
     - Output Directory: `dist`
     - Install Command: `npm install`

3. **Deploy**
   - Click "Deploy"
   - Vercel will automatically build and deploy your site

### Custom Domain Configuration

1. **Add Domain in Vercel Dashboard**
   - Go to your project settings
   - Navigate to "Domains" tab
   - Add your custom domain (e.g., `eclipse-softworks.com`)

2. **Configure DNS Records**
   Add these records to your domain provider:
   ```
   Type: A
   Name: @
   Value: ***********

   Type: CNAME
   Name: www
   Value: cname.vercel-dns.com
   ```

3. **SSL Certificate**
   - Vercel automatically provisions SSL certificates
   - Your site will be available at `https://your-domain.com`

### Environment Variables (if needed)
- In Vercel Dashboard → Project Settings → Environment Variables
- Add any required environment variables

### Build Verification
The project has been successfully built locally:
- ✅ Build command: `npm run build`
- ✅ Output directory: `dist/`
- ✅ Assets optimized and compressed
- ✅ All components transformed to space theme
- ✅ Favicon and PWA manifest configured

### Project Structure
```
Eclipse_Softworks/
├── dist/                 # Build output (auto-generated)
├── public/              # Static assets
│   ├── favicon.svg      # SVG favicon
│   ├── favicon-16x16.png
│   ├── favicon-32x32.png
│   ├── android-chrome-192x192.png
│   ├── eclipse-logo.svg # Full logo
│   └── site.webmanifest # PWA manifest
├── src/
│   ├── components/      # React components
│   ├── App.jsx         # Main app component
│   ├── main.jsx        # Entry point
│   └── index.css       # Global styles
├── vercel.json         # Vercel configuration
└── package.json        # Dependencies and scripts
```

### Performance Optimizations Included
- ✅ Vite build optimization
- ✅ CSS minification and purging
- ✅ JavaScript bundling and tree-shaking
- ✅ Image optimization
- ✅ Gzip compression
- ✅ Cache headers for static assets
- ✅ Security headers

### Post-Deployment Checklist
- [ ] Verify site loads correctly
- [ ] Test responsive design on mobile/tablet
- [ ] Check all navigation links work
- [ ] Verify contact form functionality
- [ ] Test favicon displays correctly
- [ ] Confirm custom domain SSL certificate
- [ ] Test PWA manifest (Add to Home Screen)

### Troubleshooting
- **Build fails**: Check `npm run build` works locally
- **404 errors**: Verify `vercel.json` routing configuration
- **Domain not working**: Check DNS propagation (can take 24-48 hours)
- **SSL issues**: Wait for automatic certificate provisioning

### Support
For deployment issues, contact:
- Vercel Support: [vercel.com/support](https://vercel.com/support)
- Project Developer: <EMAIL>
